import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Flex,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  Tooltip,
  Badge,
  useToast,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Button,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
  Tag,
  Card,
  CardBody,
  ScaleFade,
  Fade,
  useColorModeValue,
  Skeleton,
  SkeletonText,
  Icon,
  VStack,
  HStack,
} from "@chakra-ui/react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { IoMdArrowRoundBack } from "react-icons/io";
import { LuChevronRight } from "react-icons/lu";
import axios from "axios";
import { FaAngleDown } from "react-icons/fa";
import { useSelector } from "react-redux";
import BasicDetailsAcademy from "./BasicDetailsAcademy";
import ProfessionalDetailsAcademy from "./ProfessionalDetailsAcademy";
import KYCDetailsAcademy from "./KYCDetailsAcademy";

const AcademyDetailsPage = () => {
  const [academyData, setAcademyData] = useState({
    result: {},
    error: false,
    isLoading: false,
  });

  const [academyStatusChange, setAcademyStatusChange] = useState({
    type: "",
    id: "",
  });

  const [academyAuthStatusChange, setAcademyAuthStatusChange] = useState({
    type: "",
    id: "",
  });

  const [renderMe, setRenderMe] = useState(0);
  const userData = useSelector((state) => state.user);

  const [isOpen3, setIsOpen3] = useState(false);
  const onClose3 = () => setIsOpen3(false);
  const onOpen3 = () => setIsOpen3(true);

  const [isOpen4, setIsOpen4] = useState(false);
  const onClose4 = () => setIsOpen4(false);
  const onOpen4 = () => setIsOpen4(true);

  const navigate = useNavigate();
  const { id } = useParams();
  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];

  // Color mode values
  const bgColor = useColorModeValue("#f8fafc", "gray.900");
  const cardBg = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "gray.200");
  const mutedTextColor = useColorModeValue("gray.500", "gray.400");

  // Get academy by id and pass data through props in component
  const changeAcademyStatus = () => {
    let data = JSON.stringify({
      status: academyStatusChange?.type,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy/updateStatus/${academyStatusChange?.id}`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setRenderMe((prev) => prev + 1);
        setAcademyStatusChange({ type: "", id: "" });
        onClose3();
        toast({
          title: "Academy status updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setAcademyStatusChange({ type: "", id: "" });
        onClose3();
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const updateAcademyAuthStatus = () => {
    let data = JSON.stringify({
      authStatus: academyAuthStatusChange?.type,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy/updateAuthStatus/${academyAuthStatusChange?.id}`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setRenderMe((prev) => prev + 1);
        setAcademyAuthStatusChange({ type: "", id: "" });
        onClose4();
        toast({
          title: "Academy auth status updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setAcademyAuthStatusChange({ type: "", id: "" });
        onClose4();
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  useEffect(() => {
    setAcademyData({ result: {}, error: false, isLoading: true });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy/${id}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setAcademyData({ result: response.data.data, error: false, isLoading: false });
      })
      .catch((error) => {
        console.log(error);
        setAcademyData({ result: {}, error: true, isLoading: false });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  }, [id, renderMe, toast, token]);

  return (
    <Box bgColor={bgColor} minH="100vh" px={{ base: 2, md: 0 }}>
      <Layout title="Academy Details" content="container">
        <ScaleFade initialScale={0.9} in={true}>
          <Card bg={cardBg} shadow="sm" border="1px" borderColor={borderColor} mb={6}>
            <CardBody p={{ base: 4, md: 6 }}>
              <Flex 
                direction={{ base: "column", md: "row" }}
                justifyContent={"space-between"} 
                alignItems={{ base: "flex-start", md: "center" }}
                gap={{ base: 4, md: 0 }}
              >
                <Flex justifyContent={"center"} alignItems={"center"} flexWrap="wrap">
                  <Tooltip label="Back">
                    <Button
                      variant="ghost"
                      size={{ base: "xs", md: "sm" }}
                      mr={{ base: 2, md: 3 }}
                      leftIcon={<IoMdArrowRoundBack />}
                      onClick={() => navigate(-1)}
                      _hover={{ bg: "gray.100" }}
                    >
                      <Box display={{ base: "none", sm: "block" }}>Back</Box>
                    </Button>
                  </Tooltip>
                  <Breadcrumb 
                    fontWeight="medium" 
                    fontSize={{ base: "xs", md: "sm" }}
                    separator={<Icon as={LuChevronRight} color={mutedTextColor} />}
                  >
                    <BreadcrumbItem>
                      <BreadcrumbLink 
                        as={Link} 
                        to="/" 
                        color={mutedTextColor}
                        _hover={{ color: "blue.500" }}
                      >
                        <Box display={{ base: "none", sm: "block" }}>Dashboard</Box>
                        <Box display={{ base: "block", sm: "none" }}>Home</Box>
                      </BreadcrumbLink>
                    </BreadcrumbItem>

                    <BreadcrumbItem>
                      <BreadcrumbLink 
                        as={Link} 
                        to="/academy-page"
                        color={mutedTextColor}
                        _hover={{ color: "blue.500" }}
                      >
                        Academies
                      </BreadcrumbLink>
                    </BreadcrumbItem>

                    <BreadcrumbItem isCurrentPage>
                      <BreadcrumbLink 
                        href="#" 
                        color={textColor}
                        fontWeight="semibold"
                      >
                        <Box display={{ base: "none", md: "block" }}>
                          Update Academy 
                        </Box>
                        <Box display={{ base: "block", md: "none" }}>
                          Edit Academy
                        </Box>
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                  </Breadcrumb>
                </Flex>
                
                {userData?.accessScopes?.academy?.includes("read") &&
                  !userData?.accessScopes?.academy?.includes("write") && (
                    <Fade in={true}>
                      <HStack spacing={3}>
                        <Badge
                          colorScheme={
                            academyData?.result?.status === "active" ? "green" : "red"
                          }
                          variant="solid"
                          fontSize="0.9em"
                          px={3}
                          py={1}
                          borderRadius="full"
                        >
                          {academyData?.result?.status?.toUpperCase()}
                        </Badge>
                        <Badge
                          colorScheme={
                            academyData?.result?.authStatus === "authorized"
                              ? "green"
                              : "red"
                          }
                          variant="solid"
                          fontSize="0.9em"
                          px={3}
                          py={1}
                          borderRadius="full"
                        >
                          {academyData?.result?.authStatus?.toUpperCase()}
                        </Badge>
                      </HStack>
                    </Fade>
                  )}

                {userData?.accessScopes?.academy?.includes("write") && (
                  <Fade in={true}>
                    <HStack spacing={3} flexDirection={{ base: "column", md: "row" }}>
                      <Menu>
                        <MenuButton
                          as={Button}
                          size={{ base: "xs", md: "sm" }}
                          variant={"outline"}
                          isDisabled={academyData?.result?.authStatus !== "authorized"}
                          colorScheme={
                            academyData?.result?.status === "active" ? "green" : "red"
                          }
                          rightIcon={<FaAngleDown />}
                          _hover={{ transform: "translateY(-1px)", shadow: "md" }}
                          transition="all 0.2s"
                          fontSize={{ base: "xs", md: "sm" }}
                          w={{ base: "full", md: "auto" }}
                        >
                          {academyData?.result?.status?.toUpperCase()}
                        </MenuButton>
                        <MenuList>
                          <MenuItem
                            isDisabled={academyData?.result?.status === "active"}
                            onClick={() => {
                              setAcademyStatusChange({
                                type: "active",
                                id: academyData?.result?._id,
                              });
                              onOpen3();
                            }}
                          >
                            Active
                          </MenuItem>
                          <MenuItem
                            isDisabled={academyData?.result?.status === "inactive"}
                            onClick={() => {
                              setAcademyStatusChange({
                                type: "inactive",
                                id: academyData?.result?._id,
                              });
                              onOpen3();
                            }}
                          >
                            Inactive
                          </MenuItem>
                        </MenuList>
                      </Menu>
                      {academyData?.result?.authStatus === "authorized" ? (
                        <Tag
                          variant="solid"
                          size={"lg"}
                          colorScheme="green"
                          letterSpacing={"0.05rem"}
                          fontSize={{ base: "xs", md: "sm" }}
                          px={3}
                          py={1}
                          borderRadius="full"
                        >
                          AUTHORIZED
                        </Tag>
                      ) : (
                        <Menu>
                          <MenuButton
                            as={Button}
                            size={{ base: "xs", md: "sm" }}
                            variant={"outline"}
                            colorScheme={
                              academyData?.result?.authStatus === "authorized"
                                ? "green"
                                : "red"
                            }
                            rightIcon={<FaAngleDown />}
                            _hover={{ transform: "translateY(-1px)", shadow: "md" }}
                            transition="all 0.2s"
                            fontSize={{ base: "xs", md: "sm" }}
                            w={{ base: "full", md: "auto" }}
                          >
                            {academyData?.result?.authStatus?.toUpperCase()}
                          </MenuButton>
                          <MenuList>
                            <MenuItem
                              isDisabled={
                                academyData?.result?.authStatus === "authorized"
                              }
                              onClick={() => {
                                setAcademyAuthStatusChange({
                                  type: "authorized",
                                  id: academyData?.result?._id,
                                });
                                onOpen4();
                              }}
                            >
                              Authorized
                            </MenuItem>
                          </MenuList>
                        </Menu>
                      )}
                    </HStack>
                  </Fade>
                )}
              </Flex>
            </CardBody>
          </Card>
        </ScaleFade>
        
        {academyData.isLoading && !academyData.error ? (
          <Card bg={cardBg} shadow="sm" border="1px" borderColor={borderColor}>
            <CardBody p={{ base: 4, md: 6 }}>
              <VStack spacing={6} align="stretch">
                <HStack 
                  spacing={4} 
                  direction={{ base: "column", md: "row" }}
                  align={{ base: "stretch", md: "center" }}
                  w="full"
                >
                  <Skeleton height="40px" width={{ base: "100%", md: "120px" }} borderRadius="md" />
                  <Skeleton height="40px" width={{ base: "100%", md: "140px" }} borderRadius="md" />
                  <Skeleton height="40px" width={{ base: "100%", md: "120px" }} borderRadius="md" />
                </HStack>
                <VStack spacing={4} align="stretch">
                  <Skeleton height="20px" width={{ base: "100%", md: "60%" }} />
                  <SkeletonText mt="4" noOfLines={4} spacing="4" skeletonHeight="2" />
                  <Skeleton height="40px" width="100%" />
                  <SkeletonText mt="4" noOfLines={3} spacing="4" skeletonHeight="2" />
                </VStack>
              </VStack>
            </CardBody>
          </Card>
        ) : (
          <Fade in={!academyData.isLoading}>
            <Card bg={cardBg} shadow="sm" border="1px" borderColor={borderColor}>
              <CardBody p={0}>
                <Tabs 
                  colorScheme="blue" 
                  size={{ base: "sm", md: "md" }}
                  variant="line"
                  w="full"
                  orientation={{ base: "horizontal", lg: "horizontal" }}
                >
                  <TabList 
                    borderBottom="1px" 
                    borderColor={borderColor}
                    overflowX={{ base: "auto", md: "visible" }}
                    overflowY="hidden"
                    whiteSpace="nowrap"
                    sx={{
                      '&::-webkit-scrollbar': {
                        height: '4px'
                      },
                      '&::-webkit-scrollbar-track': {
                        background: 'transparent'
                      },
                      '&::-webkit-scrollbar-thumb': {
                        background: 'gray.300',
                        borderRadius: '2px'
                      }
                    }}
                  >
                    <Tab 
                      _selected={{ 
                        color: "blue.600", 
                        borderBottomColor: "blue.500",
                        borderBottomWidth: "3px",
                        borderBottomStyle: "solid"
                      }}
                      _hover={{ color: "blue.500" }}
                      transition="all 0.2s"
                      fontWeight="medium"
                      fontSize={{ base: "xs", md: "sm" }}
                      px={{ base: 3, md: 4 }}
                      minW={{ base: "120px", md: "auto" }}
                      color="gray.600"
                      border="none"
                      bg="transparent"
                      borderBottom="3px solid transparent"
                    >
                      <Box display={{ base: "none", sm: "block" }}>Basic Details</Box>
                      <Box display={{ base: "block", sm: "none" }}>Basic</Box>
                    </Tab>
                    <Tab 
                      _selected={{ 
                        color: "blue.600", 
                        borderBottomColor: "blue.500",
                        borderBottomWidth: "3px",
                        borderBottomStyle: "solid"
                      }}
                      _hover={{ color: "blue.500" }}
                      transition="all 0.2s"
                      fontWeight="medium"
                      fontSize={{ base: "xs", md: "sm" }}
                      px={{ base: 3, md: 4 }}
                      minW={{ base: "140px", md: "auto" }}
                      color="gray.600"
                      border="none"
                      bg="transparent"
                      borderBottom="3px solid transparent"
                    >
                      <Box display={{ base: "none", sm: "block" }}>Professional Details</Box>
                      <Box display={{ base: "block", sm: "none" }}>Professional</Box>
                    </Tab>
                    <Tab 
                      _selected={{ 
                        color: "blue.600", 
                        borderBottomColor: "blue.500",
                        borderBottomWidth: "3px",
                        borderBottomStyle: "solid"
                      }}
                      _hover={{ color: "blue.500" }}
                      transition="all 0.2s"
                      fontWeight="medium"
                      fontSize={{ base: "xs", md: "sm" }}
                      px={{ base: 3, md: 4 }}
                      minW={{ base: "100px", md: "auto" }}
                      color="gray.600"
                      border="none"
                      bg="transparent"
                      borderBottom="3px solid transparent"
                    >
                      <Box display={{ base: "none", sm: "block" }}>KYC Details</Box>
                      <Box display={{ base: "block", sm: "none" }}>KYC</Box>
                    </Tab>
                  </TabList>

                  <TabPanels>
                    <TabPanel p={{ base: 4, md: 6 }}>
                      <ScaleFade initialScale={0.95} in={true}>
                        <BasicDetailsAcademy academyData={academyData.result} />
                      </ScaleFade>
                    </TabPanel>
                    <TabPanel p={{ base: 4, md: 6 }}>
                      <ScaleFade initialScale={0.95} in={true}>
                        <ProfessionalDetailsAcademy academyData={academyData.result} />
                      </ScaleFade>
                    </TabPanel>
                    <TabPanel p={{ base: 4, md: 6 }}>
                      <ScaleFade initialScale={0.95} in={true}>
                        <KYCDetailsAcademy academyData={academyData.result} />
                      </ScaleFade>
                    </TabPanel>
                  </TabPanels>
                </Tabs>
              </CardBody>
            </Card>
          </Fade>
        )}
        
        {/* Alert Dialogs */}
        <AlertDialog isOpen={isOpen3} onClose={onClose3} isCentered>
          <AlertDialogOverlay>
            <AlertDialogContent>
              <AlertDialogHeader fontSize="lg" fontWeight="bold">
                Update Academy Status
              </AlertDialogHeader>

              <AlertDialogBody>
                When updating the academy status, an automatic email will be
                triggered to notify academy the relevant changes
              </AlertDialogBody>

              <AlertDialogFooter>
                <Button
                  colorScheme="red"
                  onClick={() => {
                    onClose3();
                    setAcademyStatusChange({ type: "", id: "" });
                  }}
                >
                  Cancel
                </Button>
                <Button
                  colorScheme="green"
                  onClick={() => {
                    changeAcademyStatus();
                  }}
                  ml={3}
                >
                  Save Changes
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialogOverlay>
        </AlertDialog>
        
        <AlertDialog isOpen={isOpen4} onClose={onClose4} isCentered>
          <AlertDialogOverlay>
            <AlertDialogContent>
              <AlertDialogHeader fontSize="lg" fontWeight="bold">
                Update Academy Auth Status
              </AlertDialogHeader>

              <AlertDialogBody>
                When updating the academy auth status, an automatic email will be
                triggered to notify academy the relevant changes
              </AlertDialogBody>

              <AlertDialogFooter>
                <Button
                  colorScheme="red"
                  onClick={() => {
                    onClose4();
                    setAcademyAuthStatusChange({ type: "", id: "" });
                  }}
                >
                  Cancel
                </Button>
                <Button
                  colorScheme="green"
                  onClick={() => {
                    updateAcademyAuthStatus();
                  }}
                  ml={3}
                >
                  Save Changes
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialogOverlay>
        </AlertDialog>
      </Layout>
    </Box>
  );
};

export default AcademyDetailsPage;
