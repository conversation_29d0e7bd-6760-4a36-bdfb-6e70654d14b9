import {
  Avatar,
  Button,
  Card,
  CardBody,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Select,
  Text,
  Tooltip,
  useToast,
  FormErrorMessage,
  InputRightElement,
  InputGroup,
  InputLeftElement,
  chakra,
  Box,
  Image,
} from "@chakra-ui/react";
import React, { useEffect, useState } from "react";
import { FaEdit } from "react-icons/fa";
import { MdCloudUpload, MdDelete } from "react-icons/md";
import ReactQuill from "react-quill";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import ReactDatePicker from "react-datepicker";
import { FaUserAlt, FaLock } from "react-icons/fa";
import "react-datepicker/dist/react-datepicker.css";
import "./datepicker.css";
import { useSelector } from "react-redux";
import { Country, State, City } from "country-state-city";
import { parseGoogleMapsResponse, isValidPincode, isGoogleMapsResponseValid } from "../../utilities/pincodeUtils";

const CFaUserAlt = chakra(FaUserAlt);
const CFaLock = chakra(FaLock);

const defaultImageUrl =
  "https://media.istockphoto.com/id/1300845620/vector/user-icon-flat-isolated-on-white-background-user-symbol-vector-illustration.jpg?s=612x612&w=0&k=20&c=yBeyba0hUkh14_jgv1OKqIH0CCSWU_4ckRkAoy2p73o=";

const BasicDetailsCoach = ({ coachData }) => {
  const [profileImagePreview, setProfileImagePreview] =
    useState(defaultImageUrl);
  const [isSubmitBtnLoading, setIsSubmitBtnLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [pincodeError, setPincodeError] = useState(false);


  const handleShowClick = () => setShowPassword(!showPassword);

  const toast = useToast();
  const navigate = useNavigate();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);
  const [states, setStates] = useState([]);

  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  const handleSelectImagesClick = () => {
    document.getElementById("fileInput").click();
  };

  const calculateAge = async (dob) => {
    const currentDate = new Date();
    const dobDate = new Date(dob);
    let age = currentDate.getFullYear() - dobDate.getFullYear();
    if (
      currentDate.getMonth() < dobDate.getMonth() ||
      (currentDate.getMonth() === dobDate.getMonth() &&
        currentDate.getDate() < dobDate.getDate())
    ) {
      age--;
    }
    return age;
  };

  const handleProfileImageChange = async (e) => {
    try {
      e.stopPropagation();
      const file = e.currentTarget.files[0];
      if (file && file.size > 10 * 1024 * 1024) {
        toast({
          title: "Please select a file less than 10 MB",
          status: "warning",
          duration: 5000,
          position: "top",
          isClosable: true,
        });
        return;
      }
      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;

      if (url) {
        toast({
          title: "Profile image uploaded",
          status: "success",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        e.target.value = null;
      } else {
        toast({
          title: "Something went wrong while uploading profile image",
          status: "error",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        e.target.value = null;
      }
      formik.setFieldValue("profileImg", url);
      setProfileImagePreview(url);
    } catch (error) {
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const deleteImageFiles = async (url) => {
    if (!userData?.accessScopes?.coach?.includes("delete")) {
      toast({
        title: "You don't have an access to perform this action",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else {
      try {
        const formData = new FormData();
        formData.append("url", url);

        const response = await axios.post(
          `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        const resp = response?.data;
        if (resp) {
          if (url) {
            toast({
              title: "Profile image deleted.",
              status: "success",
              duration: 3000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong while deleting profile image.",
              status: "error",
              duration: 3000,
              position: "top",
              isClosable: true,
            });
          }
        }
        formik.setFieldValue("profileImg", "");
        setProfileImagePreview(defaultImageUrl);
      } catch (error) {
        console.log(error);
        toast({
          title: "Something went wrong while deleting profile image.",
          status: "error",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const phoneRegExp = /^[6-9]\d{9}$/;
  const passwordRegExp =
    /^(?=.*[A-Z])(?=.*[!@#$%^&*()_+|~=`{}[\]:";'<>?,./]).{8,}$/;
  const pincodeRegex = /^\d{6}$/;
  const validationSchema = Yup.object().shape({
    firstName: Yup.string()
      .required("First name is required")
      .min(3, "First name must be at least 3 characters")
      .max(50, "Firstname must be less than or equal to 100 characters"),
    lastName: Yup.string()
      .required("Last name is required")
      .min(3, "Last name must be at least 3 characters")
      .max(50, "Last name must be less than or equal to 100 characters"),
    profileImg: (!coachData || !coachData?.profileImg)
      ? Yup.string().required("Profile image is required")
      : Yup.string(),
    email: !coachData?.email
      ? Yup.string()
        .matches(
          /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
          "Invalid email"
        )
        .required("Email is required")
      : null,
    mobile: Yup.string()
      .matches(phoneRegExp, "Invalid mobile number")
      .required("Mobile number is required"),
    alternateMobile: Yup.string().matches(phoneRegExp, "Invalid mobile number"),
    dob: Yup.date()
      .required("Date of birth is required")
      .max(
        new Date(new Date().setFullYear(new Date().getFullYear() - 18)),
        "Must be at least 18 years old"
      ),
    password: !coachData?.email
      ? Yup.string()
        .required("Password is required")
        .matches(
          passwordRegExp,
          "Password must contain at least one capital letter and one special character"
        )
      : null,
    gender: Yup.string().required("Gender is required"),
    // gstState: Yup.string()
    //   .required("GST State is required")
    //   .max(100, "Only 100 characters are allowed"),
    linkedFacilities: Yup.array().of(
      Yup.object().shape({
        name: Yup.string()
          .required("Facility name is required")
          .min(3, "Facility name must be at least 3 characters")
          .max(
            100,
            "Facility name must be less than or equal to 100 characters"
          ),
        country: Yup.string()
          .required("Country is required")
          .max(
            100,
            "Country name must be less than or equal to 100 characters"
          ),
        addressLine1: Yup.string()
          .required("Address Line 1 is required")
          .min(3, "Address Line 1 must be at least 3 characters")
          .max(
            100,
            "Address Line 1 must be less than or equal to 100 characters"
          ),
        city: Yup.string()
          .required("City is required")
          .max(100, "City name must be less than or equal to 100 characters"),
        state: Yup.string()
          .required("State is required")
          .max(100, "State name must be less than or equal to 100 characters"),
        pinCode: Yup.string()
          .matches(pincodeRegex, "Invalid pin code")
          .required("Pincode is required"),
      })
    ),
  });

  const formik = useFormik({
    initialValues: {
      firstName: coachData?.firstName || "",
      lastName: coachData?.lastName || "",
      gender: coachData?.gender || "",
      // gstState: coachData?.gstState || "",
      email: coachData?.email || "",
      mobile: coachData?.mobile || "",
      dob: coachData?.dob ? new Date(coachData.dob) : "",
      password: coachData?.email ? "***********" : "",
      profileImg: coachData?.profileImg || "",
      age: coachData?.age || "",
      alternateMobile: coachData?.alternateMobile || "",
      linkedFacilities: coachData?.linkedFacilities || [
        {
          name: "",
          addressLine1: "",
          addressLine2: "",
          city: "",
          state: "",
          pinCode: "",
          country: "",
          amenities: "",
        },
      ],
      affiliationType: coachData?.affiliationType || "academy",
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      // Handle submission logic here, e.g., API calls, etc.
      setIsSubmitBtnLoading(true);

      // Touch all fields to show validation errors
      formik.setTouched({
        ...formik.touched,
        profileImg: true,
      });

      const age = await calculateAge(values.dob);
      await formik.setFieldValue("age", age);
      await formik.setFieldValue("profileImg", `${profileImagePreview}`);

      if (coachData) {
        delete values.email;
        delete values.password;
      }

      if (pincodeError) {
        toast({
          title: "Pincode is not correct",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        setIsSubmitBtnLoading(false);
        return;
      }

      let config = coachData
        ? {
          method: "patch",
          maxBodyLength: Infinity,
          url: `${process.env.REACT_APP_BASE_URL}/api/coach/${coachData._id}`,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          data: JSON.stringify({
            ...values,
            age: age,
            affiliationType: "individual",
            academyId: userData.academyId,
          }),
        }
        : {
          method: "post",
          maxBodyLength: Infinity,
          url: `${process.env.REACT_APP_BASE_URL}/api/coach`,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          data: JSON.stringify({
            ...values,
            age: age,
            affiliationType: "individual",
            academyId: userData.academyId,
          }),
        };

      axios
        .request(config)
        .then((response) => {
          toast({
            title: coachData
              ? "Coach Profile Updated"
              : "Coach Profile Created",
            status: "success",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
          setIsSubmitBtnLoading(false);
          if (!coachData) {
            navigate(`/coach-page/details/${response?.data?.data?._id}`);
          }
        })
        .catch((error) => {
          console.log(error);
          if (error.response.status === 403) {
            toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
            setIsSubmitBtnLoading(false);
          } else if (error.response.data.error) {
            toast({
              title: error.response.data.error,
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
            setIsSubmitBtnLoading(false);
          } else {
            toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
            setIsSubmitBtnLoading(false);
          }
        });
    },
  });

  const addAddress = () => {
    formik.setValues({
      ...formik.values,
      linkedFacilities: [
        ...formik.values.linkedFacilities,
        {
          name: "",
          addressLine1: "",
          addressLine2: "",
          city: "",
          state: "",
          pinCode: "",
          country: "",
          // phone: "",
          amenities: "",
        },
      ],
    });
    // setIsFacilities(true);
  };

  const getDetailsFromPincode = async (pincode, index) => {
    try {
      if (!pincode || pincode.length === 0) {
        setPincodeError(true);
        return;
      }

      // Validate pincode format (6 digits)
      if (!isValidPincode(pincode)) {
        setPincodeError(true);
        formik.setFieldTouched(
          `linkedFacilities[${index}].pinCode`,
          "Pincode must be 6 digits"
        );
        formik.setFieldError(
          `linkedFacilities[${index}].pinCode`,
          "Pincode must be 6 digits"
        );
        return;
      }

      formik.setFieldValue(`linkedFacilities[${index}].pinCode`, pincode);

      // Use Google Maps Geocoding API
      const details = await axios.get(
        `https://maps.googleapis.com/maps/api/geocode/json?address=${pincode}&key=${process.env.REACT_APP_GOOGLE_MAPS_API_KEY}`
      );

      console.log("Google Maps API Response:", details.data);

      if (!isGoogleMapsResponseValid(details)) {
        setPincodeError(true);
        formik.setFieldTouched(
          `linkedFacilities[${index}].pinCode`,
          "Pincode is not correct"
        );
        formik.setFieldError(
          `linkedFacilities[${index}].pinCode`,
          "Pincode is not correct"
        );
        formik.setFieldValue(`linkedFacilities[${index}].city`, "");
        formik.setFieldValue(`linkedFacilities[${index}].state`, "");
        formik.setFieldValue(`linkedFacilities[${index}].country`, "");
        return;
      } else {
        setPincodeError(false);

        // Parse the Google Maps response
        const locationData = parseGoogleMapsResponse(details.data.results);

        if (locationData) {
          formik.setFieldValue(
            `linkedFacilities[${index}].city`,
            locationData.city || ""
          );
          formik.setFieldValue(
            `linkedFacilities[${index}].state`,
            locationData.state || ""
          );
          formik.setFieldValue(
            `linkedFacilities[${index}].country`,
            locationData.country || ""
          );
        } else {
          // Fallback if parsing fails
          setPincodeError(true);
          formik.setFieldError(
            `linkedFacilities[${index}].pinCode`,
            "Unable to fetch location details"
          );
        }
      }
    } catch (error) {
      console.error("Error fetching pincode details:", error);
      setPincodeError(true);
      formik.setFieldError(
        `linkedFacilities[${index}].pinCode`,
        "Error fetching location details"
      );
    }
  };



  useEffect(() => {
    if (coachData) {
      setProfileImagePreview(coachData?.profileImg || defaultImageUrl);
    }
  }, []);



  // Add helper function to check if coach is editable
  const isKhelcoachCoach = () => {
    const academyId = coachData?.academyId;
    const hasAcademyId = academyId && academyId !== "" && academyId !== null && academyId !== undefined;
    return !hasAcademyId;
  };

  // Update pointer events function
  const getPointerEvents = () => {
    if (!isKhelcoachCoach()) {
      return "none";
    }
    if (!userData?.accessScopes?.coach?.includes("write")) {
      return "none";
    }
    return "auto";
  };

  return (
    <>
      {/* Profile image */}
      <Card
        pointerEvents={getPointerEvents()}
      >
        <CardBody>
          <FormControl isInvalid={formik.touched.profileImg && formik.errors.profileImg}>
            <Flex justifyContent="space-between" alignItems="center">
              <Heading as="h4" size="md" mb={0} flexBasis="30%">
                Profile Image
              </Heading>

              <Flex flexBasis="70%" justifyContent="flex-end" alignItems="center">
                {/*  Profile image + error stacked vertically */}
                <Flex direction="column" align="center" mr={4}>
                  <Image
                    borderRadius="full"
                    boxSize="100px"
                    src={profileImagePreview}
                    alt="Profile Image"
                  />
                  <FormErrorMessage>{formik.errors.profileImg}</FormErrorMessage>
                </Flex>

                {/* Upload / Delete icons remain to the right */}
                {isKhelcoachCoach() && userData?.accessScopes?.coach?.includes("write") && (
                  <Flex>
                    <Tooltip label="Upload Profile">
                      <Text
                        as="span"
                        fontSize="22px"
                        cursor="pointer"
                        onClick={() => document.getElementById("profile-image").click()}
                      >
                        <MdCloudUpload />
                      </Text>
                    </Tooltip>
                    <Input
                      id="profile-image"
                      type="file"
                      accept="image/*"
                      style={{ display: "none" }}
                      onChange={handleProfileImageChange}
                    />
                    {defaultImageUrl !== profileImagePreview &&
                      userData?.accessScopes?.coach?.includes("delete") && (
                        <Tooltip label="Delete Profile">
                          <Text
                            ml={1}
                            as="span"
                            fontSize="22px"
                            cursor="pointer"
                            onClick={() => deleteImageFiles(profileImagePreview)}
                          >
                            <MdDelete />
                          </Text>
                        </Tooltip>
                      )}
                  </Flex>
                )}
              </Flex>
            </Flex>
          </FormControl>
        </CardBody>
      </Card>
      <form onSubmit={formik.handleSubmit}>
        {/* Personal Info */}
        <Card
          mt={4}
          pointerEvents={getPointerEvents()}
        >
          <CardBody>
            <Heading as="h4" size="md" mb={4} flexBasis={"30%"}>
              Personal Information
            </Heading>

            {!isKhelcoachCoach() && (
              <Box mb={4} p={4} bg="yellow.50" borderRadius="md" border="1px solid" borderColor="yellow.200">
                <Text color="yellow.800" fontWeight="medium" textAlign="center">
                  This Coach details can only be edited by its affiliated academy.
                </Text>
              </Box>
            )}

            <Flex justifyContent={"space-between"} alignItems={"center"} my={4}>
              <FormControl
                flexBasis={"48%"}
                isInvalid={formik.touched.firstName && formik.errors.firstName}
              >
                <FormLabel htmlFor="firstName">First Name</FormLabel>
                <Input
                  type="text"
                  placeholder="Enter first name"
                  name="firstName"
                  id="firstName"
                  isReadOnly={!isKhelcoachCoach() || !userData?.accessScopes?.coach?.includes("write")}
                  bg={!isKhelcoachCoach() ? "gray.100" : "white"}
                  {...formik.getFieldProps("firstName")}
                  autoComplete="given-name"
                />
                <FormErrorMessage>{formik.errors.firstName}</FormErrorMessage>
              </FormControl>
              <FormControl
                flexBasis={"48%"}
                isInvalid={formik.touched.lastName && formik.errors.lastName}
              >
                <FormLabel htmlFor="lastName">Last Name</FormLabel>
                <Input
                  type="text"
                  placeholder="Enter last name"
                  name="lastName"
                  id="lastName"
                  autoComplete="family-name"
                  isReadOnly={!isKhelcoachCoach() || !userData?.accessScopes?.coach?.includes("write")}
                  bg={!isKhelcoachCoach() ? "gray.100" : "white"}
                  {...formik.getFieldProps("lastName")}
                />
                <FormErrorMessage>{formik.errors.lastName}</FormErrorMessage>
              </FormControl>
            </Flex>
            <Flex justifyContent={"space-between"} alignItems={"center"} my={4}>
              <FormControl
                flexBasis={"48%"}
                isInvalid={formik.touched.mobile && formik.errors.mobile}
              >
                <FormLabel htmlFor="mobile">Mobile</FormLabel>
                <Input
                  type="number"
                  placeholder="Enter mobile number"
                  name="mobile"
                  id="mobile"
                  autoComplete="given-name"
                  {...formik.getFieldProps("mobile")}
                />
                <FormErrorMessage>{formik.errors.mobile}</FormErrorMessage>
              </FormControl>
              <FormErrorMessage>{formik.errors.mobile}</FormErrorMessage>
              <FormControl
                flexBasis={"48%"}
                isInvalid={formik.errors.alternateMobile}
              >
                <FormLabel htmlFor="alternateMobile">
                  Alternate Mobile
                </FormLabel>
                <Input
                  type="number"
                  placeholder="Enter alternate mobile number"
                  name="alternateMobile"
                  id="alternateMobile"
                  autoComplete="given-name"
                  {...formik.getFieldProps("alternateMobile")}
                />
                <FormErrorMessage>
                  {formik.errors.alternateMobile}
                </FormErrorMessage>
              </FormControl>
            </Flex>
            <Flex justifyContent={"space-between"} alignItems={"center"} my={4}>
              <FormControl
                flexBasis={"48%"}
                isInvalid={formik.touched.email && formik.errors.email}
              >
                <FormLabel htmlFor="email">Email Address</FormLabel>
                <Input
                  type="email"
                  placeholder="Enter email address"
                  name="email"
                  autoComplete="email"
                  {...formik.getFieldProps("email")}
                  isDisabled={coachData ? true : false}
                />
                <FormErrorMessage>{formik.errors.email}</FormErrorMessage>
              </FormControl>
              <FormControl
                flexBasis={"48%"}
                isInvalid={formik.touched.password && formik.errors.password}
              >
                <FormLabel htmlFor="password">Password</FormLabel>
                <InputGroup>
                  <InputLeftElement
                    pointerEvents="none"
                    color="gray.300"
                    children={<CFaLock color="gray.300" />}
                  />
                  <Input
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter password"
                    id="password"
                    name="password"
                    autoComplete="off"
                    {...formik.getFieldProps("password")}
                    isDisabled={coachData ? true : false}
                  />
                  {!coachData && (
                    <InputRightElement width="4.5rem">
                      <Button h="1.75rem" size="sm" onClick={handleShowClick}>
                        {showPassword ? "Hide" : "Show"}
                      </Button>
                    </InputRightElement>
                  )}
                </InputGroup>
                <FormErrorMessage>{formik.errors.password}</FormErrorMessage>
              </FormControl>
            </Flex>
            <Flex justifyContent={"space-between"} alignItems={"center"} my={4}>
              <FormControl
                flexBasis={"48%"}
                isInvalid={formik.touched.dob && formik.errors.dob}
              >
                <FormLabel htmlFor="dob">Date of Birth</FormLabel>
                <ReactDatePicker
                  placeholderText="Select date of birth"
                  selected={formik.values.dob}
                  onChange={(date) => formik.setFieldValue("dob", date)}
                  dateFormat="dd MMMM yyyy"
                  peekNextMonth
                  showMonthDropdown
                  showYearDropdown
                  maxDate={new Date()}
                  dropdownMode="select"
                />
                <FormErrorMessage>{formik.errors.dob}</FormErrorMessage>
              </FormControl>
              <FormControl
                flexBasis={"48%"}
                isInvalid={formik.touched.gender && formik.errors.gender}
              >
                <FormLabel htmlFor="gender ">Gender</FormLabel>
                <Select
                  placeholder="Select Gender"
                  type="text"
                  name="gender"
                  id="gender"
                  autoComplete="family-name"
                  isDisabled={!isKhelcoachCoach() || !userData?.accessScopes?.coach?.includes("write")}
                  bg={!isKhelcoachCoach() ? "gray.100" : "white"}
                  {...formik.getFieldProps("gender")}
                >
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </Select>
                <FormErrorMessage>{formik.errors.gender}</FormErrorMessage>
              </FormControl>

              {/* <FormControl
                flexBasis={"48%"}
                isInvalid={formik.touched.gstState && formik.errors.gstState}
              >
                <FormLabel htmlFor="gstState">GST State</FormLabel>
                <Select
                  placeholder="Select GST State"
                  name="gstState"
                  id="gstState"
                  autoComplete="address-level1"
                  {...formik.getFieldProps("gstState")}
                >
                  <option value="">Select State</option>
                  {states.map((state) => (
                    <option key={state.isoCode} value={state.isoCode}>
                      {state.name}
                    </option>
                  ))}
                </Select>
                <FormErrorMessage>{formik.errors.gstState}</FormErrorMessage>
              </FormControl> */}
            </Flex>


          </CardBody>
        </Card>
        {/* Address */}
        <Card
          my={4}
          bgColor={"gray.50"}
          pointerEvents={
            (!userData?.accessScopes?.coach?.includes("write") || coachData?.affiliationType === "academy") ? "none" : "auto"
          }
        >
          <CardBody>
            <Flex justifyContent={"space-between"} alignItems={"center"}>
              <Heading as="h4" size="md" mb={0} flexBasis={"30%"}>
                Address
              </Heading>
              <Button
                colorScheme="green"
                size={"sm"}
                px={4}
                onClick={addAddress}
              >
                Add
              </Button>
            </Flex>
            {formik?.values?.linkedFacilities?.map((address, index) => {
              return (
                <Card my={6} key={index}>
                  <CardBody>
                    <Flex
                      justifyContent={"space-between"}
                      alignItems={"center"}
                      mb={4}
                    >
                      <Heading as="h4" size="md" mb={0}>
                        {"Address " + (index + 1)}
                      </Heading>
                      {index + 1 > 1 ? (
                        <Button
                          colorScheme="red"
                          size={"sm"}
                          px={4}
                          onClick={() =>
                            formik.setValues((prevState) => ({
                              ...prevState,
                              linkedFacilities:
                                prevState.linkedFacilities.filter(
                                  (_, inx) => inx !== index
                                ),
                            }))
                          }
                          isDisabled={coachData?.affiliationType === "academy"}
                        >
                          Remove
                        </Button>
                      ) : null}
                    </Flex>
                    <Divider />
                    <Flex
                      justifyContent={"space-between"}
                      alignItems={"center"}
                      my={4}
                    >
                      <FormControl
                        flexBasis={"48%"}
                        isInvalid={
                          formik.touched.linkedFacilities?.[index]?.name &&
                          formik.errors.linkedFacilities?.[index]?.name
                        }
                      >
                        <FormLabel htmlFor={`linkedFacilities.${index}.name`}>
                          Facility Name
                        </FormLabel>
                        <Input
                          type="text"
                          name={`linkedFacilities.${index}.name`}
                          id={`linkedFacilities.${index}.name`}
                          autoComplete="given-name"
                          placeholder="Enter facility name"
                          {...formik.getFieldProps(
                            `linkedFacilities.${index}.name`
                          )}
                          isReadOnly={coachData?.affiliationType === "academy"}
                        />
                        {formik.touched.linkedFacilities?.[index]?.name &&
                          formik.errors.linkedFacilities?.[index]?.name && (
                            <Text color="red.500" fontSize="sm" mt={1}>
                              {formik.errors.linkedFacilities?.[index]?.name}
                            </Text>
                          )}
                      </FormControl>
                      <FormControl
                        flexBasis={"48%"}
                        isInvalid={
                          formik.touched.linkedFacilities?.[index]?.pinCode &&
                          formik.errors.linkedFacilities?.[index]?.pinCode
                        }
                      >
                        <FormLabel
                          htmlFor={`linkedFacilities.${index}.pincode`}
                        >
                          Pincode
                        </FormLabel>
                        <Input
                          type="number"
                          placeholder="Enter pincode"
                          name={`linkedFacilities.${index}.pinCode`}
                          id={`linkedFacilities.${index}.pinCode`}
                          value={formik.values.linkedFacilities[index].pinCode}
                          onChange={(e) => {
                            formik.setFieldValue(`linkedFacilities.${index}.pinCode`, e.target.value);
                            getDetailsFromPincode(e.target.value, index);
                          }}
                        />
                        {formik.touched.linkedFacilities?.[index]?.pinCode &&
                          formik.errors.linkedFacilities?.[index]?.pinCode && (
                            <Text color="red.500" fontSize="sm" mt={1}>
                              {formik.errors.linkedFacilities?.[index]?.pinCode}
                            </Text>
                          )}
                      </FormControl>
                    </Flex>
                    <Flex
                      justifyContent={"space-between"}
                      alignItems={"center"}
                      my={4}
                    >
                      <FormControl
                        flexBasis={"48%"}
                        isInvalid={
                          formik.touched.linkedFacilities?.[index]
                            ?.addressLine1 &&
                          formik.errors.linkedFacilities?.[index]?.addressLine1
                        }
                      >
                        <FormLabel
                          htmlFor={`linkedFacilities.${index}.addressLine1`}
                        >
                          Address Line 1
                        </FormLabel>
                        <Input
                          type="text"
                          placeholder="Enter address line 1"
                          name={`linkedFacilities.${index}.addressLin1`}
                          id={`linkedFacilities.${index}.addressLine1`}
                          autoComplete="given-name"
                          {...formik.getFieldProps(
                            `linkedFacilities.${index}.addressLine1`
                          )}
                          isReadOnly={coachData?.affiliationType === "academy"}
                        />
                        {formik.touched.linkedFacilities?.[index]
                          ?.addressLine1 &&
                          formik.errors.linkedFacilities?.[index]
                            ?.addressLine1 && (
                            <Text color="red.500" fontSize="sm" mt={1}>
                              {
                                formik.errors.linkedFacilities?.[index]
                                  ?.addressLine1
                              }
                            </Text>
                          )}
                      </FormControl>
                      <FormControl flexBasis={"48%"}>
                        <FormLabel
                          htmlFor={`linkedFacilities.${index}.addressLine2`}
                        >
                          Address Line 2
                        </FormLabel>
                        <Input
                          type="text"
                          placeholder="Enter address line 2"
                          name={`linkedFacilities.${index}.addressLine2`}
                          id={`linkedFacilities.${index}.addressLine2`}
                          autoComplete="family-name"
                          {...formik.getFieldProps(
                            `linkedFacilities.${index}.addressLine2`
                          )}
                          isReadOnly={coachData?.affiliationType === "academy"}
                        />
                      </FormControl>
                    </Flex>
                    <Flex
                      justifyContent={"space-between"}
                      alignItems={"center"}
                      my={4}
                    >
                      <FormControl
                        flexBasis={"31%"}
                        isInvalid={
                          formik.touched.linkedFacilities?.[index]?.city &&
                          formik.errors.linkedFacilities?.[index]?.city
                        }
                      >
                        <FormLabel htmlFor={`linkedFacilities.${index}.city`}>
                          City
                        </FormLabel>
                        <Input
                          type="text"
                          placeholder="Enter city name"
                          name={`linkedFacilities.${index}.city`}
                          id={`linkedFacilities.${index}.city`}
                          autoComplete="family-name"
                          {...formik.getFieldProps(
                            `linkedFacilities.${index}.city`
                          )}
                          isReadOnly={coachData?.affiliationType === "academy"}
                        />
                        {formik.touched.linkedFacilities?.[index]?.city &&
                          formik.errors.linkedFacilities?.[index]?.city && (
                            <Text color="red.500" fontSize="sm" mt={1}>
                              {formik.errors.linkedFacilities?.[index]?.city}
                            </Text>
                          )}
                      </FormControl>
                      <FormControl
                        flexBasis={"31%"}
                        isInvalid={
                          formik.touched.linkedFacilities?.[index]?.state &&
                          formik.errors.linkedFacilities?.[index]?.state
                        }
                      >
                        <FormLabel htmlFor={`linkedFacilities.${index}.state`}>
                          State
                        </FormLabel>
                        <Input
                          type="text"
                          placeholder="Enter state name"
                          name={`linkedFacilities.${index}.state`}
                          id={`linkedFacilities.${index}.state`}
                          autoComplete="family-name"
                          {...formik.getFieldProps(
                            `linkedFacilities.${index}.state`
                          )}
                          isReadOnly={coachData?.affiliationType === "academy"}
                        />
                        {formik.touched.linkedFacilities?.[index]?.state &&
                          formik.errors.linkedFacilities?.[index]?.state && (
                            <Text color="red.500" fontSize="sm" mt={1}>
                              {formik.errors.linkedFacilities?.[index]?.state}
                            </Text>
                          )}
                      </FormControl>
                      <FormControl
                        flexBasis={"31%"}
                        isInvalid={
                          formik.touched.linkedFacilities?.[index]?.country &&
                          formik.errors.linkedFacilities?.[index]?.country
                        }
                      >
                        <FormLabel
                          htmlFor={`linkedFacilities.${index}.country`}
                        >
                          Country
                        </FormLabel>
                        <Input
                          type="text"
                          placeholder="Enter country name"
                          name={`linkedFacilities.${index}.country`}
                          id={`linkedFacilities.${index}.country`}
                          autoComplete="family-name"
                          {...formik.getFieldProps(
                            `linkedFacilities.${index}.country`
                          )}
                          isReadOnly={coachData?.affiliationType === "academy"}
                        />
                        {formik.touched.linkedFacilities?.[index]?.country &&
                          formik.errors.linkedFacilities?.[index]?.country && (
                            <Text color="red.500" fontSize="sm" mt={1}>
                              {formik.errors.linkedFacilities?.[index]?.country}
                            </Text>
                          )}
                      </FormControl>
                    </Flex>
                    <FormControl flexBasis={"48%"}>
                      <FormLabel
                        htmlFor={`linkedFacilities.${index}.amenities`}
                      >
                        Ameneties
                      </FormLabel>
                      <ReactQuill
                        theme="snow"
                        value={formik.values.linkedFacilities[index].amenities}
                        onChange={(value) => {
                          formik.setFieldValue(
                            `linkedFacilities.${index}.amenities`,
                            value
                          );
                        }}
                        readOnly={coachData?.affiliationType === "academy"}
                      />
                    </FormControl>
                  </CardBody>
                </Card>
              );
            })}
          </CardBody>
        </Card>
        {isKhelcoachCoach() &&
          userData?.accessScopes?.coach?.includes("write") &&
          coachData?.affiliationType !== "academy" && (
            <Flex justifyContent={"space-between"} alignItems={"center"}>
              <Button
                colorScheme="red"
                flexBasis={"49%"}
                onClick={() => {
                  formik.resetForm();
                  toast({
                    title: "All Changes has been discarded",
                    status: "success",
                    duration: 4000,
                    position: "top",
                    isClosable: true,
                  });
                }}
                isDisabled={coachData?.affiliationType === "academy"}
              >
                Discard
              </Button>
              <Button
                colorScheme="green"
                flexBasis={"49%"}
                type="submit"
                isLoading={isSubmitBtnLoading}
                isDisabled={coachData?.affiliationType === "academy"}
              >
                {coachData ? "Update" : "Submit"}
              </Button>
            </Flex>
          )}
        {coachData?.affiliationType === "academy" && (
          <Box
            p={4}
            bg="gray.100"
            borderRadius="md"
            textAlign="center"
            mt={4}
          >
            <Text color="gray.600" fontSize="sm">
              This coach is affiliated with an academy. Basic details cannot be modified.
            </Text>
          </Box>
        )}
      </form>
    </>
  );
};

export default BasicDetailsCoach;
