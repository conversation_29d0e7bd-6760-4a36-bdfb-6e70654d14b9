import { useEffect, useRef, useState } from "react";
import Layout from "../../layout/default";
import {
    Box,
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    Flex,
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    Menu,
    MenuButton,
    MenuList,
    MenuItem,
    Button,
    useToast,
    Spinner,
    Tooltip,
    HStack,
    AlertDialog,
    AlertDialogOverlay,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogBody,
    AlertDialogFooter,
} from "@chakra-ui/react";
import { Link, useNavigate } from "react-router-dom";
import { FaTrash } from "react-icons/fa";
import { FaCircleArrowRight } from "react-icons/fa6";
import axios from "axios";
import ReactPaginate from "react-paginate";

const BlogPost = () => {
    const [blogs, setBlogs] = useState([]);
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [deleteId, setDeleteId] = useState(null);
    const [isDeleteOpen, setIsDeleteOpen] = useState(false);
    const cancelRef = useRef();

    const limit = 20;
    const toast = useToast();
    const navigate = useNavigate();

    const token = sessionStorage.getItem("admintoken")
        ? sessionStorage.getItem("admintoken").split(" ")[1]
        : null;

    const fetchBlogs = async (page = 1) => {
        setLoading(true);
        try {
            let config = {
                method: "get",
                url: `${process.env.REACT_APP_BASE_URL}/api/blogs?page=${page}&limit=${limit}`,
                headers: {
                    "Content-Type": "application/json",
                    Authorization: token ? `Bearer ${token}` : "",
                },
            };

            const res = await axios.request(config);
            const { blogs, blogsCount } = res?.data?.data;

            setBlogs(blogs || []);
            setTotalPages(Math.ceil((blogsCount || 0) / limit));
            setCurrentPage(page);
        } catch (err) {
            console.error(err);
            toast({
                title: "Failed to fetch blogs",
                status: "error",
                duration: 4000,
                position: "top",
                isClosable: true,
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (token) {
            fetchBlogs(currentPage);
        }
    }, [token, currentPage]);

    const confirmDelete = (id) => {
        setDeleteId(id);
        setIsDeleteOpen(true);
    };

    const handleDelete = async () => {
        try {
            let config = {
                method: "delete",
                url: `${process.env.REACT_APP_BASE_URL}/api/blogs/${deleteId}`,
                headers: {
                    "Content-Type": "application/json",
                    Authorization: token ? `Bearer ${token}` : "",
                },
            };

            await axios.request(config);
            setBlogs((prev) => prev.filter((b) => b._id !== deleteId));

            toast({
                title: "Blog deleted successfully",
                status: "success",
                duration: 3000,
                position: "top",
            });
        } catch (err) {
            console.error(err);
            toast({
                title: "Failed to delete blog",
                status: "error",
                duration: 3000,
                position: "top",
            });
        } finally {
            setIsDeleteOpen(false);
            setDeleteId(null);
        }
    };

    const handlePageChange = (data) => {
        const selectedPage = data.selected + 1;
        fetchBlogs(selectedPage);
    };

    const handleVisibilityToggle = async (id, currentVisibility) => {
        try {
            const updatedVisibility = !currentVisibility;

            let config = {
                method: "patch",
                url: `${process.env.REACT_APP_BASE_URL}/api/blogs/${id}`,
                headers: {
                    "Content-Type": "application/json",
                    Authorization: token ? `Bearer ${token}` : "",
                },
                data: { isVisible: updatedVisibility },
            };

            await axios.request(config);

            setBlogs((prev) =>
                prev.map((b) =>
                    b._id === id ? { ...b, isVisible: updatedVisibility } : b
                )
            );

            toast({
                title: `Blog marked as ${updatedVisibility ? "VISIBLE" : "HIDDEN"
                    }`,
                status: "success",
                duration: 3000,
                position: "top",
            });
        } catch (err) {
            console.error(err);
            toast({
                title: "Failed to update visibility",
                status: "error",
                duration: 3000,
                position: "top",
            });
        }
    };

    return (
        <Layout title="CMS | Blog Post" content="container">
            {/* Breadcrumb */}
            <Flex justifyContent={"space-between"} alignItems={"center"} mb={6}>
                <Breadcrumb fontWeight="medium" fontSize="sm">
                    <BreadcrumbItem>
                        <BreadcrumbLink as={Link} to={"/"}>
                            Dashboard
                        </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbItem isCurrentPage>
                        <BreadcrumbLink>CMS</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbItem isCurrentPage>
                        <BreadcrumbLink href="#">Blog Post</BreadcrumbLink>
                    </BreadcrumbItem>
                </Breadcrumb>
                <Box>
                    <Link to={"/cms/blog"}>
                        <Button
                            variant={"outline"}
                            colorScheme="teal"
                            size={"sm"}
                            py={5}
                            px={4}
                            mr={3}
                        >
                            Create Blog Post
                        </Button>
                    </Link>
                </Box>
            </Flex>

            {/* Page Wrapper */}
            <Flex direction="column" minH="calc(100vh - 170px)">
                {/* Table */}
                <Box
                    borderWidth="1px"
                    borderRadius="md"
                    overflow="auto"
                    maxH="calc(100vh - 280px)"
                    flex="1"
                >
                    {loading ? (
                        <Flex justify="center" align="center" py={15}>
                            <Spinner size="lg" />
                        </Flex>
                    ) : blogs.length === 0 ? (
                        <Flex justify="center" align="center" py={20}>
                            <Box fontSize="lg" fontWeight="medium" color="gray.500">
                                No blogs found
                            </Box>
                        </Flex>
                    ) : (
                        <Table variant="simple" w="100%" tableLayout="fixed">
                            <Thead bgColor={"#E2DFDF"}>
                                <Tr>
                                    <Th>Blog Name</Th>
                                    <Th textAlign="center">Author</Th>
                                    <Th textAlign="center">Publish Date</Th>
                                    <Th textAlign="center">Visibility</Th>
                                    <Th textAlign="center" w="150px">
                                        Action
                                    </Th>
                                </Tr>
                            </Thead>
                            <Tbody>
                                {blogs?.map((blog) => (
                                    <Tr key={blog?._id} height="50px">
                                        <Td py={1} fontSize="sm" lineHeight="short">{blog?.blogName}</Td>
                                        <Td textAlign="center" py={1} fontSize="sm" lineHeight="short">{blog?.writerName}</Td>
                                        <Td textAlign="center" py={1} fontSize="sm" lineHeight="short">
                                            {blog?.publishDate
                                                ? (() => {
                                                    const date = new Date(blog?.publishDate);
                                                    const day = String(date.getDate()).padStart(2, "0");
                                                    const month = String(date.getMonth() + 1).padStart(2, "0");
                                                    const year = date.getFullYear();
                                                    return `${day}-${month}-${year}`;
                                                })()
                                                : "N/A"}
                                        </Td>
                                        <Td textAlign="center" py={1}>
                                            <Menu>
                                                <MenuButton
                                                    as={Button}
                                                    size="xs"
                                                    variant="outline"
                                                    colorScheme={blog.isVisible ? "green" : "red"}
                                                    borderColor={blog.isVisible ? "green.500" : "red.500"}
                                                >
                                                    {blog.isVisible ? "VISIBLE" : "HIDDEN"}
                                                </MenuButton>
                                                <MenuList>
                                                    <MenuItem
                                                        isDisabled={blog.isVisible}
                                                        onClick={() => handleVisibilityToggle(blog._id, blog.isVisible)}
                                                    >
                                                        Visible
                                                    </MenuItem>
                                                    <MenuItem
                                                        isDisabled={!blog.isVisible}
                                                        onClick={() => handleVisibilityToggle(blog._id, blog.isVisible)}
                                                    >
                                                        Hidden
                                                    </MenuItem>
                                                </MenuList>
                                            </Menu>
                                        </Td>
                                        <Td textAlign="center" py={1}>
                                            <HStack spacing={3} justify="center">
                                                <Tooltip label="Edit Blog" placement="top">
                                                    <Box
                                                        as="span"
                                                        fontSize="18px"
                                                        cursor="pointer"
                                                        onClick={() => navigate(`/cms/blog/${blog._id}`)}
                                                    >
                                                        <FaCircleArrowRight />
                                                    </Box>
                                                </Tooltip>
                                                <Tooltip label="Delete Blog" placement="top">
                                                    <Box
                                                        as="span"
                                                        fontSize="18px"
                                                        cursor="pointer"
                                                        onClick={() => confirmDelete(blog._id)}
                                                        _hover={{ color: "red.500" }}
                                                    >
                                                        <FaTrash />
                                                    </Box>
                                                </Tooltip>
                                            </HStack>
                                        </Td>
                                    </Tr>

                                ))}
                            </Tbody>
                        </Table>
                    )}
                </Box>

                {/* Pagination */}
                {blogs.length > 0 && (
                    <Flex justifyContent="center" mt={6} mb={4}>
                        <ReactPaginate
                            previousLabel={"Previous"}
                            nextLabel={"Next"}
                            breakLabel={"..."}
                            pageCount={totalPages}
                            marginPagesDisplayed={2}
                            pageRangeDisplayed={5}
                            onPageChange={handlePageChange}
                            containerClassName={"pagination"}
                            activeClassName={"active"}
                            forcePage={currentPage - 1}
                        />
                    </Flex>
                )}
            </Flex>

            {/* Delete Confirmation Modal */}
            <AlertDialog
                isOpen={isDeleteOpen}
                leastDestructiveRef={cancelRef}
                onClose={() => setIsDeleteOpen(false)}
            >
                <AlertDialogOverlay>
                    <AlertDialogContent>
                        <AlertDialogHeader fontSize="lg" fontWeight="bold">
                            Delete Blog
                        </AlertDialogHeader>
                        <AlertDialogBody>
                            Are you sure you want to delete this blog? This action cannot be
                            undone.
                        </AlertDialogBody>
                        <AlertDialogFooter>
                            <Button ref={cancelRef} onClick={() => setIsDeleteOpen(false)}>
                                Cancel
                            </Button>
                            <Button colorScheme="red" onClick={handleDelete} ml={3}>
                                Delete
                            </Button>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialogOverlay>
            </AlertDialog>
        </Layout>
    );
};

export default BlogPost;

