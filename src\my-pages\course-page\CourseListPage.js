import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import { IoFilter } from "react-icons/io5";

import {
  Box,
  BreadcrumbLink,
  Input,
  Breadcrumb,
  BreadcrumbItem,
  Card,
  CardBody,
  Button,
  Stack,
  Text,
  Flex,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  TableContainer,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  useToast,
  Spinner,
  Tooltip,
  Badge,
  Select,
  InputGroup,
  InputRightAddon,
} from "@chakra-ui/react";
import { Link, useNavigate } from "react-router-dom";
import axios from "axios";
import ReactPaginate from "react-paginate";
import { useSelector } from "react-redux";
import { IoIosClose, IoMdSearch } from "react-icons/io";

const CourseListPage = () => {
  const [courseData, setCourseData] = useState({
    result: [],
    isLoading: false,
    error: false,
    notFound: false,
  });

  const [coachSearchDetails, setCoachSearchDetails] = useState({
    result: [],
    isLoading: false,
    error: false,
  });

  const [searchQueryCoach, setSearchQueryCoach] = useState("");
  const [searchCourseName, setSearchCourseName] = useState("");
  const [selectedClassType, setSelectedClassType] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");

  const [isSearched, setIsSearched] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [showSearch, setShowSearch] = useState(false);

  const [isOpen2, setIsOpen2] = useState(false);
  const onClose2 = () => setIsOpen2(false);
  const onOpen2 = () => setIsOpen2(true);

  const toast = useToast();
  const navigate = useNavigate();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getCourseData = (searchCourseName, classType, status) => {
    setCourseData({
      result: [],
      isLoading: true,
      error: false,
      notFound: false,
    });
    let queryString = "?";

    if (searchCourseName) {
      queryString += `courseName=${searchCourseName}`;
    } else {
      queryString = `?page=${currentPage}`;
    }

    if (status) {
      queryString += `${queryString ? "&" : ""}status=${status}`;
    }

    if (classType) {
      queryString += `${queryString ? "&" : ""}classType=${classType}`;
    }

    let config = {
      method: "get",
      url: `${process.env.REACT_APP_BASE_URL}/api/course/${
        queryString ? `${queryString}` : ""
      }`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setCourseData({
          result: response.data.data,
          isLoading: false,
          error: false,
          notFound: response.data.data.length === 0 ? true : false,
        });
        setTotalPages(Math.ceil(response.data.totalResults / 25));
      })
      .catch((error) => {
        console.log(error);
        setCourseData({
          result: [],
          isLoading: false,
          error: true,
          notFound: false,
        });
        if (error.response.status === 403) {
          setCourseData({
            result: [],
            isLoading: false,
            error: true,
            notFound: false,
          });
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const getCoaches = async (name, page, getAllCoaches = false) => {
    let url = "";
    if (name) {
      url += `${process.env.REACT_APP_BASE_URL}/api/coach?firstName=${name}`;
    } else if (getAllCoaches) {
      url += `${process.env.REACT_APP_BASE_URL}/api/coach?getAllCoaches=false`;
    } else {
      url += `${process.env.REACT_APP_BASE_URL}/api/coach?page=${page}`;
    }

    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: url,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setCoachSearchDetails({
          result: response.data.data,
          isLoading: false,
          error: false,
        });
        if (response.data.data.length === 0) {
          setIsSearched(true);
        }
      })
      .catch((error) => {
        console.log(error);
        setCoachSearchDetails({ result: [], isLoading: false, error: true });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handlePageChange = ({ selected }) => {
    setCurrentPage(selected + 1);
  };

  useEffect(() => {
    getCourseData(searchCourseName, selectedClassType, selectedStatus);
  }, [currentPage, searchCourseName, selectedClassType, selectedStatus]);

  useEffect(() => {
    getCoaches("", 1, true); // Pass false for normal pagination
  }, []);

  return (
    <Layout title="Course" content="container">
      <Flex
        w={"100%"}
        justifyContent={"space-between"}
        alignItems={"center"}
        mb={6}
      >
        {showSearch ? (
          <Box flexBasis={"58%"}>
            <InputGroup size="md">
              <Input
                pr="4.5rem"
                type="text"
                placeholder="Search"
                borderColor={"gray.300"}
                onChange={(e) => {
                  if (e.target.value.length >= 3) {
                    setTimeout(() => {
                      setSearchCourseName(e.target.value);
                    }, 500);
                  }
                  if (e.target.value.length === 0) {
                    setSearchCourseName("");
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    if (e.target.value.length >= 3) {
                      setSearchCourseName(e.target.value);
                    }
                  }
                }}
              />
              <InputRightAddon
                bgColor={"gray.300"}
                border={"1px"}
                borderColor={"gray.300"}
                onClick={() => {
                  setShowSearch(false);
                  setSearchCourseName("");
                }}
                cursor={"pointer"}
              >
                <IoIosClose fontSize={"24px"} />
              </InputRightAddon>
            </InputGroup>
          </Box>
        ) : (
          <Flex
            flexBasis={"54%"}
            justifyContent={"space-between"}
            alignItems={"center"}
          >
            <Breadcrumb fontWeight="medium" fontSize="sm">
              <BreadcrumbItem>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbItem>

              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Training Schedule</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
            <Text
              display={"flex"}
              px={4}
              justifyContent={"center"}
              alignItems={"center"}
              py={"7px"}
              border={"1px"}
              borderColor={"gray.300"}
              rounded={"md"}
              color="gray.500"
              cursor={"pointer"}
              onClick={() => setShowSearch(true)}
            >
              <IoMdSearch fontSize={"24px"} />
              <IoFilter fontSize={"22px"} ml={1} />
            </Text>
          </Flex>
        )}
        <Flex flexBasis={"45%"} justifyContent={"space-between"} gap={1}>
          <Box flexBasis={"33%"}>
            <Select
              placeholder="Class Type"
              borderColor={"gray.300"}
              cursor={"pointer"}
              bgColor={selectedClassType && "gray.300"}
              onChange={(e) => {
                setSelectedClassType(e.target.value);
                setCurrentPage(1);
              }}
            >
              <option value="">All</option>
              <option value="class">Class</option>
              <option value="course">Course</option>
            </Select>
          </Box>
          <Box flexBasis={"31%"}>
            <Select
              placeholder="Status"
              borderColor={"gray.300"}
              cursor={"pointer"}
              bgColor={selectedStatus && "gray.300"}
              onChange={(e) => {
                setSelectedStatus(e.target.value);
                setCurrentPage(1);
              }}
            >
              <option value="">All</option>
              <option value="active">Active</option>
              <option value="inactive">In-Active</option>
            </Select>
          </Box>
          <Box flexBasis={"31%"}>
            <Button
              variant={"outline"}
              colorScheme="teal"
              size={"sm"}
              px={4}
              isDisabled={!userData?.accessScopes?.course?.includes("write")}
              onClick={() => {
                onOpen2();
                setIsSearched(false);
                setSearchQueryCoach("");
                getCoaches("", 1, true); // Pass true to trigger getAllCoaches=false
              }}
              width={"100%"}
              height={"100%"}
              mt={{ base: 2, md: 0 }}
            >
              Create Training Schedule
            </Button>
          </Box>
        </Flex>
      </Flex>
      {/* Added/Selected Course List */}
      {!courseData?.isLoading && courseData?.error ? (
        <Flex
          justifyContent={"center"}
          alignItems={"center"}
          w={"full"}
          my={10}
        >
          <Text color={"red.500"}>
            Something went wrong please try again later...
          </Text>
        </Flex>
      ) : (
        <TableContainer
          mt={6}
          height={`${window.innerHeight - 235}px`}
          overflowY={"scroll"}
        >
          <Table variant="simple">
            <Thead
              bgColor={"#c1eaee"}
              position={"sticky"}
              top={"0px"}
              zIndex={"99"}
            >
              <Tr bgColor={"#E2DFDF"}>
                <Th>Training Schedule Name</Th>
                <Th>Academy Name</Th>
                <Th>Coach Name</Th>
                <Th>Type</Th>
                <Th>Status</Th>
                <Th>Category</Th>
                <Th>Enrolled</Th>
              </Tr>
            </Thead>
            <Tbody>
              {courseData?.isLoading && !courseData?.error ? (
                <Tr>
                  <Td></Td>
                  <Td> </Td>
                  <Td
                    display={"flex"}
                    justifyContent={"flex-end"}
                    alignItems={"center"}
                  >
                    <Spinner />
                  </Td>
                  <Td></Td>
                  <Td></Td>
                  <Td></Td>
                </Tr>
              ) : !courseData?.notFound ? (
                courseData?.result?.map((course, inx) => {
                  return (
                    <Tr
                      key={course._id}
                      cursor={"pointer"}
                      onClick={() =>
                        navigate(`/course-page/details/${course?._id}`)
                      }
                    >
                      <Td
                        style={{
                          whiteSpace: "pre-wrap",
                          wordWrap: "break-word",
                        }}
                        fontSize={"14px"}
                      >
                        {course?.courseName || "n/a"}
                      </Td>
                      <Td
                        style={{
                          whiteSpace: "pre-wrap",
                          wordWrap: "break-word",
                        }}
                        fontSize={"14px"}
                      >
                        {course?.academy_id?.name || "Khelcoach"}
                      </Td>
                      <Td
                        style={{
                          whiteSpace: "pre-wrap",
                          wordWrap: "break-word",
                        }}
                        fontSize={"14px"}
                      >
                        {course?.coachName || "n/a"}
                      </Td>
                      <Td fontSize={"14px"}>
                        {course?.classType?.charAt(0)?.toUpperCase() +
                          course?.classType?.slice(1) || "n/a"}
                      </Td>
                      <Td fontSize={"14px"}>
                        {
                          <Badge
                            colorScheme={
                              course?.status === "active" ? "green" : "red"
                            }
                          >
                            {course?.status?.toUpperCase()}
                          </Badge>
                        }
                      </Td>
                      <Td fontSize={"14px"}>{course?.category}</Td>
                      <Td fontSize={"14px"}>{course?.playerEnrolled}</Td>
                    </Tr>
                  );
                })
              ) : (
                <Tr>
                  <Td></Td>
                  <Td></Td>

                  <Td
                    display={"flex"}
                    justifyContent={"flex-end"}
                    alignItems={"center"}
                  >
                    <Text color={"green.500"} fontWeight={"semibold"}>
                      No result found
                    </Text>
                  </Td>
                  <Td> </Td>
                  <Td></Td>
                  <Td></Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </TableContainer>
      )}
      {/* Pagination */}
      {!courseData?.notFound && (
        <Flex
          justifyContent="center"
          alignItems="center"
          flexDirection={"row"}
          w={"100%"}
          mt={5}
        >
          <ReactPaginate
            previousLabel="Previous"
            nextLabel="Next"
            breakLabel="..."
            pageCount={totalPages}
            marginPagesDisplayed={2}
            pageRangeDisplayed={5}
            onPageChange={handlePageChange}
            containerClassName="pagination"
            activeClassName="active"
            forcePage={currentPage - 1}
          />
        </Flex>
      )}

      {/* Modal for coach select to add course */}
      <Modal isOpen={isOpen2} onClose={onClose2} size="5xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Select Coach</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Card mt={3} variant="outline" height={"95%"}>
              <CardBody>
                <Box>
                  <Stack direction="row" spacing={4} align="center">
                    <Input
                      type="text"
                      placeholder="Search Coach"
                      value={searchQueryCoach}
                      onChange={(e) => {
                        setSearchQueryCoach(e.target.value);
                        setIsSearched(false);
                      }}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          getCoaches(searchQueryCoach, 1);
                          setIsSearched(false);
                        }
                      }}
                    />
                    <Button
                      variant="solid"
                      colorScheme="telegram"
                      onClick={() => {
                        getCoaches(searchQueryCoach, 1);
                        setIsSearched(false);
                      }}
                      isDisabled={!(searchQueryCoach.length > 0)}
                    >
                      Search
                    </Button>
                  </Stack>
                  <Card mt={6}>
                    {!coachSearchDetails?.isLoading &&
                    coachSearchDetails?.error ? (
                      <Flex
                        justifyContent={"center"}
                        alignItems={"center"}
                        w={"full"}
                        my={10}
                      >
                        <Text color={"red.500"}>
                          Something went wrong please try again later...
                        </Text>
                      </Flex>
                    ) : (
                      <TableContainer
                        height={`${window.innerHeight - 300}px`}
                        overflowY={"scroll"}
                      >
                        <Table variant="simple">
                          <Thead
                            bgColor={"#c1eaee"}
                            position={"sticky"}
                            top={"0px"}
                            zIndex={"99"}
                          >
                            <Tr bgColor={"#E2DFDF"}>
                              <Th>S.No</Th>
                              <Th>Name</Th>
                              <Th>Gender</Th>
                              <Th>Experience</Th>
                              <Th>Status</Th>
                              <Th>Auth Status</Th>
                              <Th>Action</Th>
                            </Tr>
                          </Thead>
                          <Tbody>
                            {coachSearchDetails?.isLoading &&
                            !coachSearchDetails?.error ? (
                              <Tr>
                                <Td></Td>
                                <Td></Td>
                                <Td></Td>
                                <Td
                                  display={"flex"}
                                  justifyContent={"center"}
                                  alignItems={"center"}
                                >
                                  <Spinner />
                                </Td>
                                <Td></Td>
                                <Td></Td>
                                <Td></Td>
                              </Tr>
                            ) : !coachSearchDetails?.isLoading &&
                              coachSearchDetails?.error ? (
                              <Flex
                                justifyContent={"center"}
                                alignItems={"center"}
                                w={"full"}
                                my={10}
                              >
                                <Text color={"red.500"}>
                                  Something went wrong please try again later...
                                </Text>
                              </Flex>
                            ) : !isSearched ? (
                              coachSearchDetails?.result?.map((coach, i) => {
                                const isAcademyCoach = coach?.affiliationType === "academy";
                                return (
                                  <Tr key={i}>
                                    <Td fontSize={"14px"}>{i + 1 + "."}</Td>
                                    <Td
                                      style={{
                                        whiteSpace: "pre-wrap",
                                        wordWrap: "break-word",
                                      }}
                                      fontSize={"14px"}
                                    >
                                      {coach?.firstName + " " + coach?.lastName}
                                      {isAcademyCoach && (
                                        <Badge colorScheme="red" fontSize="xs">Academy Coach</Badge>
                                      )}
                                    </Td>
                                    <Td fontSize={"14px"}>
                                      {coach?.gender?.toUpperCase() || "n/a"}
                                    </Td>
                                    <Td textAlign={"center"} fontSize={"14px"}>
                                      {coach?.experience
                                        ? coach?.experience + " years"
                                        : "n/a"}
                                    </Td>
                                    <Td fontSize={"14px"}>
                                      <Badge
                                        colorScheme={
                                          coach?.status === "active"
                                            ? "green"
                                            : "red"
                                        }
                                        opacity={isAcademyCoach ? 0.5 : 1}
                                        cursor={isAcademyCoach ? "not-allowed" : "default"}
                                      >
                                        {coach?.status?.toUpperCase()}
                                      </Badge>
                                    </Td>
                                    <Td fontSize={"14px"}>
                                      {" "}
                                      <Badge
                                        colorScheme={
                                          coach?.authStatus === "authorized"
                                            ? "green"
                                            : "red"
                                        }
                                        opacity={isAcademyCoach ? 0.5 : 1}
                                        cursor={isAcademyCoach ? "not-allowed" : "default"}
                                      >
                                        {coach?.authStatus?.toUpperCase()}
                                      </Badge>
                                    </Td>
                                    <Td textAlign={"center"} fontSize={"14px"}>
                                      {userData?.accessScopes?.course?.includes(
                                        "read"
                                      ) && (
                                        <>
                                          {coach?.googleEmail?.length > 0 &&
                                          coach?.refreshToken?.length > 0 ? (
                                            coach?.authStatus ===
                                              "authorized" &&
                                            coach?.status === "active" ? (
                                              <Tooltip label={isAcademyCoach ? "Academy coaches cannot be selected" : "Select Coach"}>
                                                <Text
                                                  as={"span"}
                                                  fontSize={"22px"}
                                                  cursor={isAcademyCoach ? "not-allowed" : "pointer"}
                                                  onClick={() => {
                                                    if (!isAcademyCoach) {
                                                      navigate(
                                                        `/course-page/creation/${coach?._id}`
                                                      );
                                                    }
                                                  }}
                                                >
                                                  <Button
                                                    colorScheme="green"
                                                    size={"sm"}
                                                    isDisabled={isAcademyCoach}
                                                  >
                                                    Select
                                                  </Button>
                                                </Text>
                                              </Tooltip>
                                            ) : (
                                              <Tooltip
                                                label={
                                                  "Course is only created, when coach is active and authorized"
                                                }
                                              >
                                                <Text
                                                  as={"span"}
                                                  fontSize={"22px"}
                                                  cursor={"pointer"}
                                                >
                                                  <Button
                                                    colorScheme="green"
                                                    size={"sm"}
                                                    isDisabled={true}
                                                  >
                                                    Select
                                                  </Button>
                                                </Text>
                                              </Tooltip>
                                            )
                                          ) : (
                                            <Tooltip
                                              label={
                                                "Coach Account is not Linked with Google Calendar"
                                              }
                                            >
                                              <Badge
                                                colorScheme="yellow"
                                                cursor={"default"}
                                              >
                                                Not Linked
                                              </Badge>
                                            </Tooltip>
                                          )}
                                        </>
                                      )}
                                    </Td>
                                  </Tr>
                                );
                              })
                            ) : (
                              <Tr>
                                <Td></Td>
                                <Td></Td>
                                <Td></Td>
                                <Td
                                  display={"flex"}
                                  justifyContent={"flex-end"}
                                >
                                  <Text
                                    color={"green.500"}
                                    fontWeight={"semibold"}
                                  >
                                    No result found
                                  </Text>
                                </Td>
                                <Td></Td>
                                <Td></Td>
                                <Td></Td>
                              </Tr>
                            )}
                          </Tbody>
                        </Table>
                      </TableContainer>
                    )}
                  </Card>
                </Box>
              </CardBody>
            </Card>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Layout>
  );
};

export default CourseListPage;
