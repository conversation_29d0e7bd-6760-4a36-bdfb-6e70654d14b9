import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Card,
  CardBody,
  Divider,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Image,
  Input,
  Select,
  Stack,
  Text,
  Tooltip,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  UnorderedList,
  ListItem,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverHeader,
  PopoverBody,
  PopoverArrow,
  PopoverCloseButton,
  AlertDialog,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogOverlay,
  useDisclosure,
  Menu,
  MenuButton,
  MenuList,
  Checkbox,
  HStack,
  Tag,
} from "@chakra-ui/react";
import { IoMdArrowRoundBack } from "react-icons/io";
import { FaExternalLinkAlt } from "react-icons/fa";
import { FaChevronDown } from "react-icons/fa";
import { Link, useNavigate, useParams } from "react-router-dom";
import ReactQuill from "react-quill";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "./datepicker.css";
import axios from "axios";
import moment from "moment-timezone";
import { useSelector } from "react-redux";

const proficiencyLevelOption = [
  { id: 0, name: "Beginner", value: "beginner" },
  { id: 1, name: "Intermediate", value: "intermediate" },
  { id: 2, name: "Advance", value: "advance" },
];

const CourseUpdate = ({ courseData, renderMe }) => {
  const [selectedImages, setSelectedImages] = useState(
    courseData?.images || []
  );
  const [selectedImagesError, setSelectedImagesError] = useState(false);
  const [courseName, setCourseName] = useState(courseData?.courseName || "");
  const [courseNameError, setCourseNameError] = useState(false);
  const [isItCamp, setIsItCamp] = useState(courseData?.camp ? true : false);
  const [campName, setCampName] = useState(courseData?.campName || "");
  const [campNameError, setCampNameError] = useState(false);
  const [maximumGroupSize, setMaximumGroupSize] = useState(
    courseData?.maxGroupSize || 1
  );
  const [maximumGroupSizeError, setMaximumGroupSizeError] = useState(false);
  const [sessionType, setSessionType] = useState(
    courseData?.sessionType?.toLowerCase() || "group"
  );
  const [categoryType, setCategoryType] = useState(courseData?.category || "");
  const [categories, setCategories] = useState([]);
  const [courseDescription, setCourseDescription] = useState(
    courseData?.description || ""
  );
  const [isCustomImage, setIsCustomImage] = useState(
    courseData?.customImage || false
  );
  const [courseDescriptionError, setCourseDescriptionError] = useState(false);
  const [courseAmeneties, setCourseAmeneties] = useState(
    courseData?.amenitiesProvided || ""
  );
  const [btnIsLoading, setBtnIsLoading] = useState(false);
  const [facilities, setFacilities] = useState([]);
  const [selectedDays, setSelectedDays] = useState(
    courseData?.dates?.days || []
  );
  const [isEnd, setIsEnd] = useState(false);
  const [timeFrom, setTimeFrom] = useState("");
  const [timeTo, setTimeTo] = useState("");
  const [classType, setClassType] = useState(courseData?.classType || "class");
  const [startDate, setStartDate] = useState(
    courseData?.dates?.startDate || ""
  );
  const [startDateError, setStartDateError] = useState(false);
  const [endDate, setEndDate] = useState(courseData?.dates?.endDate || "");
  const [endDateError, setEndDateError] = useState(false);

  const [price, setPrice] = useState(courseData?.fees?.feesCourse || "");
  const [priceError, setPriceError] = useState(false);
  const [fees30, setFees30] = useState(courseData?.fees?.fees30 || "");
  const [fees60, setFees60] = useState(courseData?.fees?.fees60 || "");
  const [fees30Error, setFees30Error] = useState(false);
  const [fees60Error, setFees60Error] = useState(false);
  const [cancellationPolicy, setCancellationPolicy] = useState(
    courseData?.cancellationPolicy || ""
  );
  const [carryThings, setCarryThings] = useState(
    courseData?.whatYouHaveToBring || ""
  );
  const [timeFromError, setTimeFromError] = useState(false);
  const [timeToError, setTimeToError] = useState(false);
  const [courseFacility, setCourseFacility] = useState(
    courseData?.facility || ""
  );
  const [categoryError, setCategoryError] = useState(false);
  const [facilityError, setFacilityError] = useState(false);
  const [selectedDaysError, setSelectedDaysError] = useState(false);
  const [coach, setCoach] = useState({});
  const [proficiencyLevel, setProficiencyLevel] = useState([]);
  const [showSlotConflict, setShowSlotConflict] = useState(false);
  const [conflictResult, setConflictResult] = useState({});

  const handleStateUpdate = () => {
    const updatedProficiencies = proficiencyLevelOption
      .filter((x) => courseData?.proficiency?.includes(x.value))
      .map((x) => x.value);

    setProficiencyLevel(updatedProficiencies);
  };
  useEffect(() => {
    handleStateUpdate();
  }, [courseData]);

  function formatDateToYYYYMMDD(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed

    return `${year}-${month}-${day}`;
  }

  const { isOpen, onOpen, onClose } = useDisclosure();

  const setTimeField = (date, time) => {
    const value = new Date(formatDateToYYYYMMDD(date)); // Your time value
    value.setHours(time?.split(":")[0]); // Set the hours to 13 (1:00 PM)
    value.setMinutes(time?.split(":")[1]); // Set the minutes to 0
    return value;
  };

  const toast = useToast();
  const navigate = useNavigate();
  const { id } = useParams();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

  const getCurrentTime = () => {
    const currentDate = new Date();
    const currentHours = currentDate.getHours();
    const currentMinutes = currentDate.getMinutes();
    return new Date(0, 0, 0, currentHours, currentMinutes);
  };

  const currentTime = getCurrentTime();

  const date = new Date(startDate);
  const today = new Date();

  const includesToday =
    date.getDate() <= today.getDate() &&
    date.getMonth() <= today.getMonth() &&
    date.getFullYear() <= today.getFullYear();

  // const filterPassedTime = (time) => {
  //   if (!timeFrom) return true;
  //   const selectedTime = new Date(time);
  //   const minAllowedTime = getMinTime();
  //   return selectedTime >= minAllowedTime;
  // };

  const minTime = includesToday ? currentTime : undefined;

  const getMinTime = () => {
    if (!timeFrom) return null;

    const timeFromObj = new Date(timeFrom);
    const thirtyMinutesLater = new Date(timeFromObj.getTime() + 30 * 60000); // 30 minutes in milliseconds

    return thirtyMinutesLater;
  };

  // Validate 30-minute gap between start and end time
  const validateTimeGap = () => {
    if (!timeFrom || !timeTo) return true; // Skip validation if times are not set

    // Calculate time difference in milliseconds
    const startTimeMs = timeFrom.getTime();
    const endTimeMs = timeTo.getTime();
    const timeDifference = endTimeMs - startTimeMs;
    const thirtyMinutesInMs = 30 * 60 * 1000; // 30 minutes in milliseconds

    // Return true if the gap is 30 minutes or more, false if less than 30 minutes
    return timeDifference >= thirtyMinutesInMs;
  };

  function daysDifference(startDate, endDate, daysArray) {
    const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const start = new Date(startDate);
    const end = new Date(endDate);

    let count = 0;
    let currentDate = new Date(start);

    while (currentDate <= end) {
      if (daysArray.includes(daysOfWeek[currentDate.getDay()])) {
        count++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return count;
  }

  function getLastDateOfCurrentYear() {
    var today = new Date(); // Get current date
    var currentYear = today.getFullYear(); // Get current year
    var lastDate = new Date(currentYear, 11, 31); // Set to December 31st of the current year
    return formatDateToYYYYMMDD(lastDate);
  }

  const getTime = (time) => {
    const tempdate = new Date(time);

    const hours = tempdate.getHours(); // Get the hour component (0-23)
    const minutes = tempdate.getMinutes(); // Get the minute component (0-59)

    // Format the time as needed (e.g., to a string with leading zeros)
    const formattedTime = `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}`;

    return formattedTime;
  };

  // Add helper function to check if course is editable
  const isKhelcoachCourse = () => {
    const academyId = courseData?.academy_id?._id;
    console.log("Course - Academy ID:", academyId);
    
    // If academyId exists, then it's NOT a Khelcoach course (not editable)
    const hasAcademyId = academyId && academyId !== "" && academyId !== null && academyId !== undefined;
    
    return !hasAcademyId;
  };

  // Check if start date is in the past or current date
  const isStartDateInPastOrToday = () => {
    if (!courseData?.dates?.startDate) return false;
    const courseStartDate = moment(courseData.dates.startDate).tz("Asia/Kolkata");
    const today = moment().tz("Asia/Kolkata");
    return courseStartDate.isSameOrBefore(today, "day");
  };

  const getPointerEvents = () => {
    if (!isKhelcoachCourse()) return "none";
    if (isStartDateInPastOrToday()) return "none";
    return !userData?.accessScopes?.course?.includes("write") ? "none" : "auto";
  };

  // ### Final Save ###
  const saveHandler = async () => {
    setBtnIsLoading(true);
    let lastDateOfYear = getLastDateOfCurrentYear();
    lastDateOfYear = formatDateToYYYYMMDD(lastDateOfYear);
    if (sessionType === "individual") {
      setMaximumGroupSize(1);
    }

    if (!timeFrom || !timeTo) {
      if (!timeTo) {
        setTimeToError(true);
      }
      if (!timeFrom) {
        setTimeFromError(true);
      }
      setBtnIsLoading(false);
    }

    // Validate 30-minute gap between start and end time
    if (timeFrom && timeTo && !validateTimeGap()) {
      setBtnIsLoading(false);
      toast({
        title: "Invalid time range",
        description: "End time must be at least 30 minutes after start time",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
      return;
    }

    if (classType === "class") {
      if (fees30 === "" && !fees60) {
        setFees30Error(true);
      }
      if (fees60 === "" && !fees30) {
        setFees60Error(true);
      }
      if (fees30 === "" || fees60 === "") {
        setBtnIsLoading(false);
      }
    }

    if (classType !== "class") {
      if (price === "") {
        setBtnIsLoading(false);
        setPriceError(true);
      }
    }

    if (!categoryType) {
      setCategoryError(true);
      setBtnIsLoading(false);
    }

    if (!courseFacility) {
      setFacilityError(true);
      setBtnIsLoading(false);
    }

    if (!courseDescription || courseDescription === "<p><br></p>") {
      setCourseDescriptionError(true);
      setBtnIsLoading(false);
    }

    if (!selectedDays.length) {
      setBtnIsLoading(false);
      setSelectedDaysError(true);
    }

    if (!selectedImages.length) {
      setBtnIsLoading(false);
      setSelectedImagesError(true);
    }

    if (!startDate) {
      setBtnIsLoading(false);
      setStartDateError(true);
    }

    if (!endDate) {
      setBtnIsLoading(false);
      setEndDateError(true);
    }

    if (courseName.length <= 3) {
      setBtnIsLoading(false);
      setCourseNameError(true);
    }

    if (classType === "course" && isItCamp ? !campName : false) {
      setCampNameError(true);
      setBtnIsLoading(false);
    }

    if (
      Number(maximumGroupSize) &&
      Math.sign(Number(maximumGroupSize)) === -1 &&
      sessionType === "group" &&
      classType === "course"
    ) {
      setMaximumGroupSizeError(true);
      setBtnIsLoading(false);
    }

    // Validations //
    if (!id) {
      setBtnIsLoading(true);
      toast({
        title: "Please select coach, before creating the course",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
      return;
    }

    if (
      !(
        daysDifference(
          new Date(startDate),
          endDate ? new Date(endDate) : lastDateOfYear,
          selectedDays
        ) >= selectedDays.length
      )
    ) {
      setBtnIsLoading(false);
      toast({
        title: "Selected Days is not available between selected date range",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
      return;
    }

    const startDateTime = moment.tz(startDate, "Asia/Kolkata").toISOString();
    const endDateTime = isEnd
      ? moment
          .tz(endDate ? endDate : lastDateOfYear, "Asia/Kolkata")
          .toISOString()
      : moment.tz(lastDateOfYear, "Asia/Kolkata").toISOString();

    let isAvailable = await getAvailableSlots(`${id}`);

    if (!isAvailable) {
      setBtnIsLoading(false);
      toast({
        title: "Selected slots are already booked",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
      return;
    }

    const selectedUrl = categories.filter((x) => x.name === categoryType);
    // main working body //
    let raw = JSON.stringify({
      courseName: `${courseName.replace(/\s+/g, " ").trim()}`,
      description: `${courseDescription}`,
      images:
        selectedImages.length <= 0
          ? [{ url: selectedUrl[0]?.image }]
          : selectedImages,
      coach_id: `${courseData?.coach_id?._id}` || "",
      coachName: (coach?.firstName || "") + " " + (coach?.lastName || ""),
      category: `${categoryType}`,
      customImage: selectedImages.length <= 0 ? false : true,
      sessionType:
        classType === "course" ? `${sessionType.toLowerCase()}` : "individual",
      classType: classType,
      camp: isItCamp,
      campName: `${campName.replace(/\s+/g, " ").trim()}`,
      fees: {
        feesCourse: classType !== "class" ? Number(price) : "",
        fees30: classType === "class" ? Number(fees30) : "",
        fees60: classType === "class" ? Number(fees60) : "",
        fees:
          classType === "class"
            ? fees30 && fees60
              ? Number(fees30)
              : fees30
              ? Number(fees30)
              : Number(fees60)
            : Number(price),
      },
      maxGroupSize: maximumGroupSize !== "" ? maximumGroupSize : 1,
      amenitiesProvided: courseAmeneties,
      proficiency: proficiencyLevel,
      facility: courseFacility,
      coachEmail: coach.email,
      dates: {
        startDate: startDateTime,
        endDate: endDateTime,
        startTime: getTime(timeFrom),
        endTime: getTime(timeTo),
        days: selectedDays,
      },
      whatYouHaveToBring: carryThings,
      cancellationPolicy: cancellationPolicy,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/course/${id}`,
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      data: raw,
    };

    axios
      .request(config)
      .then((response) => {
        toast({
          title: "Course Updated successfully",
          status: "success",
          duration: 4000,
          isClosable: true,
          position: "top",
        });
        renderMe();
        setBtnIsLoading(false);
        setCourseDescriptionError(false);
        setCourseName("");
        setCourseNameError(false);
        setCampName("");
        setCampNameError(false);
        setMaximumGroupSize("");
        setMaximumGroupSizeError(false);
        setCourseDescription("");
        setCourseAmeneties("");
        setStartDateError(false);
        setEndDate("");
        setPriceError(false);
        setFees30("");
        setFees60("");
        setStartDate("");
        setTimeFrom("");
        setSelectedDaysError(false);
        setSelectedDays([]);
        setCancellationPolicy("");
        setCarryThings("");
        setCategoryType({});
        setCourseFacility({});
        setTimeTo("");
        setFees30Error(false);
        setFees60Error(false);
        setSelectedImages([]);
        setCategoryError(false);
        setFacilityError(false);
        setBtnIsLoading(false);
        setSelectedImagesError(false);
      })
      .catch((error) => {
        setBtnIsLoading(false);
        if(error.response?.data?.error === "Start date cannot be in the past") {
          toast({
            title: "Start date cannot be in the past",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
        console.log(error);
      });
  };

  const getCategories = async () => {
    try {
      let response = await axios.get(
        `${process.env.REACT_APP_BASE_URL}/api/category`
      );
      setCategories(response.data.data);
    } catch (error) {
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const getCoachDetails = () => {
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/coach/${courseData?.coach_id?._id}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        if (response.data.error) {
          toast({
            title: response.data.error,
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
          navigate("/course-page");
        } else {
          setFacilities(response?.data?.linkedFacilities);
          setCoach(response?.data);
        }
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const handleRadioChange = (e) => {
    const value = e.target.value === "yes";
    setIsItCamp(value);
  };

  const slotDateFormatter = (date) => {
    const myDate = new Date(date);
    myDate.setFullYear(2024, 11, 31);
    myDate.setHours(13, 0, 0, 0);
    return myDate.toISOString();
  };

  function convertStartDateFormat(inputDateString) {
    let inputDate = new Date(inputDateString);
    inputDate.setDate(inputDate.getDate() + 1);
    const year = inputDate.getFullYear();
    const month = ("0" + (inputDate.getMonth() + 1)).slice(-2);
    const day = ("0" + (inputDate.getDate() - 1)).slice(-2);
    const formattedDateString = year + "-" + month + "-" + day;
    return formattedDateString;
  }

  function convertEndDateFormat(inputDateString) {
    let inputDate = new Date(inputDateString);
    const year = inputDate.getFullYear();
    const month = ("0" + (inputDate.getMonth() + 1)).slice(-2);
    const day = ("0" + inputDate.getDate()).slice(-2);
    const formattedDateString = year + "-" + month + "-" + day;
    return formattedDateString;
  }

  const getAvailableSlots = async (id, value, endTime) => {
    try {
      let lastDateOfYear = getLastDateOfCurrentYear();
      const startDateTime = `${convertStartDateFormat(startDate)}T${getTime(
        timeFrom
      )}:00`;
      const endDateTime = `${
        endDate ? convertEndDateFormat(endDate) : lastDateOfYear
      }T${timeTo !== "" ? getTime(timeTo) : getTime(endTime)}:00`;

      let data = JSON.stringify({
        dates: {
          startDate: startDateTime,
          endDate: endDateTime,
          startTime: getTime(timeFrom),
          endTime: endTime ? getTime(endTime) : getTime(timeTo),
          days: selectedDays,
          courseId: id,
        },
      });

      let config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/course/availableSlots/${courseData?.coach_id?._id}`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: data,
      };

      let response = await axios.request(config);
      setConflictResult(response.data);
      if (response.data.message === "Slots available") {
        setShowSlotConflict(false);
        return true;
      } else {
        setShowSlotConflict(true);
        return false;
      }
    } catch (error) {
      setBtnIsLoading(false);
      toast({
        title: "Something went wrong please try again later",
        status: "error",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    }
  };

  const formatSlotDate = (date) => {
    const originalDateString = date;
    const originalDate = new Date(originalDateString);

    const year = originalDate.getUTCFullYear();
    const month = originalDate.getUTCMonth() + 1;
    const day = originalDate.getUTCDate();

    const formattedDateString = `${day}-${
      month < 10 ? "0" : ""
    }${month}-${year}`;

    return formattedDateString;
  };

  const handleFileChange = async (e) => {
    try {
      const files = Array.from(e.currentTarget.files);
      if (!files.length) return;
      
      setSelectedImagesError(false);

      // Check if adding these files would exceed the limit
      if (selectedImages.length + files.length > 10) {
        toast({
          title: "Maximum 10 images allowed",
          description: `You can only upload ${10 - selectedImages.length} more image(s)`,
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        e.target.value = "";
        return;
      }

      // Check file sizes
      const oversizedFiles = files.filter(file => file.size > 10 * 1024 * 1024);
      if (oversizedFiles.length > 0) {
        toast({
          title: "File size too large",
          description: "Please select files less than 10 MB each",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        e.target.value = "";
        return;
      }

      const uploadPromises = files.map(async (file) => {
        const formData = new FormData();
        formData.append("image", file);

        const response = await axios.post(
          `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        return response?.data?.url;
      });

      const uploadedUrls = await Promise.all(uploadPromises);
      const validUrls = uploadedUrls.filter(url => url);
      
      if (validUrls.length > 0) {
        setSelectedImages([...selectedImages, ...validUrls.map(url => ({ url }))]);
        toast({
          title: `${validUrls.length} image(s) uploaded successfully`,
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
      
      e.target.value = "";
    } catch (error) {
      if (error.response?.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
      e.target.value = "";
    }
  };

  const deleteImageFiles = async (url, index) => {
    try {
      if (isCustomImage) {
        const formData = new FormData();
        formData.append("url", url);

        await axios.post(
          `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        toast({
          title: "Image removed Successfully",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        setSelectedImages([]);
      }
    } catch (error) {
      console.log(error);
      toast({
        title:
          "Something went wrong while removing image, please try again later",
        status: "error",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  useEffect(() => {
    getCategories();
    if (courseData?.coach_id?._id) {
      getCoachDetails();
    }
    classType === "class" && setIsEnd(true);
    moment.tz(endDate, "Asia/Kolkata").toISOString() ===
      moment.tz(getLastDateOfCurrentYear(), "Asia/Kolkata").toISOString() &&
      setIsEnd(false);
  }, [courseData]);

  useEffect(() => {
    setTimeFrom(setTimeField(startDate, courseData?.dates?.startTime) || "");
    setTimeTo(setTimeField(startDate, courseData?.dates?.endTime) || "");
  }, []);

  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="Update Course" content="container">
        {/* Breadcrumb */}
        <Flex justifyContent={"space-between"} alignItems={"center"} mb={4}>
          <Flex justifyContent={"center"} alignItems={"center"}>
            <Tooltip label="Back">
              <Text
                mr={3}
                as={"span"}
                fontSize={"26px"}
                cursor={"pointer"}
                onClick={() => navigate(-1)}
              >
                <IoMdArrowRoundBack />
              </Text>
            </Tooltip>
            <Breadcrumb fontWeight="medium" fontSize="sm">
              <BreadcrumbItem>
                <Link to={"/"}>Dashboard</Link>
              </BreadcrumbItem>

              <BreadcrumbItem>
                <Link to="/course-page">Course</Link>
              </BreadcrumbItem>

              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Details </BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          {userData?.accessScopes?.course?.includes("read") && (
            <Popover>
              <PopoverTrigger>
                <Button colorScheme="linkedin">Coach Details</Button>
              </PopoverTrigger>
              <PopoverContent>
                <PopoverArrow />
                <PopoverCloseButton
                  color={"white"}
                  fontWeight={"semibold"}
                  mt={1}
                />
                <PopoverHeader bgColor={"#00A0DC"}>
                  <Flex>
                    <Text as={"span"} color={"white"} fontWeight={"semibold"}>
                      Coach Profile
                    </Text>
                    {/* {userData?.accessScopes?.coach?.includes("read") && ( */}
                    <Text ml={2} mt={1} fontSize={"sm"} color={"white"}>
                      <Link
                        to={`/coach-page/details/${courseData?.coach_id?._id}`}
                      >
                        <FaExternalLinkAlt />
                      </Link>
                    </Text>
                    {/* )} */}
                  </Flex>
                </PopoverHeader>
                <PopoverBody>
                  <Text m={0}>{coach?.firstName + " " + coach?.lastName}</Text>
                  <Divider my={1} />
                  <Text m={0}>{coach?.email || "n/a"}</Text>
                  <Divider my={1} />
                  <Text m={0}>{coach?.mobile || "n/a"}</Text>
                  <Divider my={1} />
                  <Text m={0}>
                    {coach?.age ? coach?.age + " Years old" : "n/a"}
                  </Text>
                  <Divider my={1} />
                  <Text m={0}>{coach?.gender?.toUpperCase() || "n/a"}</Text>
                  <Divider my={1} />
                </PopoverBody>
              </PopoverContent>
            </Popover>
          )}
        </Flex>
        <Card
          bgColor={"#f9f9f9"}
          pointerEvents={getPointerEvents()}
        >
          <CardBody>
            {/* Warning message for academy courses */}
            {!isKhelcoachCourse() && (
              <Box
                bg="orange.50"
                border="1px"
                borderColor="orange.200"
                borderRadius="md"
                p={4}
                mb={4}
              >
                <Text color="orange.800" fontWeight="medium" textAlign={"center"}>
                   Course details can only be edited for Khelcoach coaches
                </Text>
              </Box>
            )}
            {/* Warning message for past start date courses */}
            {isKhelcoachCourse() && isStartDateInPastOrToday() && (
              <Box
                bg="red.50"
                border="1px"
                borderColor="red.200"
                borderRadius="md"
                p={4}
                mb={4}
              >
                <Text color="red.800" fontWeight="medium" textAlign={"center"}>
                  You cannot edit past courses
                </Text>
              </Box>
            )}
            <Card>
              <CardBody>
                {/* Course name */}
                <FormControl isInvalid={courseNameError}>
                  <FormLabel>Course Name</FormLabel>
                  <Input
                    type="text"
                    isReadOnly={!isKhelcoachCourse() || !userData?.accessScopes?.course?.includes("write")}
                    placeholder="Enter course name"
                    name="course-name"
                    id="course-name"
                    value={courseName}
                    autoComplete="off"
                    onChange={(e) => {
                      setCourseName(e.target.value);
                      if (e.target.value !== "" && e.target.value.length >= 2) {
                        setCourseNameError(false);
                      } else {
                        setCourseNameError(true);
                      }
                      if (e.target.value.length > 100) {
                        setCourseNameError(true);
                      } else {
                        setCourseNameError(false);
                      }
                    }}
                  />
                  {courseNameError && (
                    <FormErrorMessage>
                      Please enter course name, atleast 3 characters and maximum
                      100 characters
                    </FormErrorMessage>
                  )}
                </FormControl>
                {/* Description */}
                <FormControl mt={6} isInvalid={courseDescriptionError}>
                  <FormLabel>Description</FormLabel>
                  {userData?.accessScopes?.course?.includes("write") ? (
                    <ReactQuill
                      theme="snow"
                      value={courseDescription}
                      onChange={(value) => {
                        setCourseDescription(value);
                        if (value === "" || value === "<p><br></p>") {
                          setCourseDescriptionError(true);
                        } else {
                          setCourseDescriptionError(false);
                        }
                      }}
                    />
                  ) : (
                    <ReactQuill
                      theme="snow"
                      value={courseDescription}
                      readOnly={!isKhelcoachCourse() || !userData?.accessScopes?.course?.includes("write")}
                      onChange={(value) => {
                        setCourseDescription(value);
                        if (value === "" || value === "<p><br></p>") {
                          setCourseDescriptionError(true);
                        } else {
                          setCourseDescriptionError(false);
                        }
                      }}
                    />
                  )}

                  {courseDescriptionError && (
                    <FormErrorMessage>
                      Please enter description
                    </FormErrorMessage>
                  )}
                </FormControl>
              </CardBody>
            </Card>
            <Card
              mt={4}
              pointerEvents={
                !userData?.accessScopes?.course?.includes("write")
                  ? "none"
                  : "auto"
              }
            >
              <CardBody>
                {/* Course Image */}
                <FormControl mt={6}>
                  <FormLabel>Course Images ({selectedImages.length}/10)</FormLabel>
                  <Box mt={3}>
                    {userData?.accessScopes?.course?.includes("write") && (
                      <Input
                        id={`kycDocuments.documentImg.${0}.url`}
                        name={`kycDocuments.documentImg.${0}.url`}
                        type="file"
                        accept="image/*"
                        multiple
                        disabled={!isKhelcoachCourse() || selectedImages.length >= 10}
                        onChange={(e) => handleFileChange(e, 0)}
                      />
                    )}
                    {selectedImagesError && (
                      <Text color={"red.500"} fontSize={"sm"} mt={1}>
                        Please select at least one course image
                      </Text>
                    )}
                    {selectedImages.length >= 10 && (
                      <Text color={"orange.500"} fontSize={"sm"} mt={1}>
                        Maximum 10 images allowed
                      </Text>
                    )}
                    {selectedImages.length > 0 && (
                      <Box display="flex" flexWrap="wrap" mt={3}>
                        {selectedImages.map((preview, index) => (
                          <Box key={index} m={1} textAlign={"center"}>
                            <a
                              href={`${preview?.url}`}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <Image
                                src={preview?.url}
                                alt={`Preview ${index}`}
                                height="11vh"
                                width="7vw"
                              />
                            </a>
                            {userData?.accessScopes?.course?.includes(
                              "delete"
                            ) && (
                              <Button
                                colorScheme="red"
                                size="sm"
                                mt={1}
                                onClick={() => {
                                  deleteImageFiles(preview?.url, index);
                                  setSelectedImages(
                                    selectedImages.filter(
                                      (image) => image.url !== preview.url
                                    )
                                  );
                                }}
                              >
                                Remove
                              </Button>
                            )}
                          </Box>
                        ))}
                      </Box>
                    )}
                  </Box>
                </FormControl>
              </CardBody>
            </Card>

            {/*  Slot */}
            <Card
              mt={4}
              pointerEvents={
                !userData?.accessScopes?.course?.includes("write")
                  ? "none"
                  : "auto"
              }
            >
              <CardBody>
                {/* Radion btn - session type */}
                <Flex justifyContent={"center"} alignItems={"center"}>
                  <Stack direction="row">
                    {classType === "class" ? (
                      <Box mr={6}>
                        <input
                          id="notification-method-class"
                          type="radio"
                          value=""
                          required
                          checked={classType === "class"}
                          onChange={(e) =>
                            e.target.checked && setClassType("class")
                          }
                        />
                        <label
                          htmlFor="notification-method-class"
                          className="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-black-300"
                          style={{
                            cursor: "pointer",
                            fontSize: "1.1rem",
                            fontWeight: "600",
                          }}
                        >
                          Session
                        </label>
                      </Box>
                    ) : (
                      <Box>
                        <input
                          id="notification-method-course"
                          type="radio"
                          value=""
                          required
                          checked={classType === "course"}
                          onChange={(e) =>
                            e.target.checked && setClassType("course")
                          }
                        />
                        <label
                          htmlFor="notification-method-course"
                          className="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-black-300"
                          style={{
                            cursor: "pointer",
                            fontSize: "1.1rem",
                            fontWeight: "600",
                          }}
                        >
                          Course
                        </label>
                      </Box>
                    )}
                  </Stack>
                </Flex>
                {/* Start Date */}
                <FormControl my={3} isInvalid={startDateError}>
                  <FormLabel>Start Date</FormLabel>
                  {/* <Input
                    type="date"
                    placeholder="Select start date"
                    value={new Date(startDate)}
                    min={formatDateToYYYYMMDD(new Date())}
                    autoComplete="off"
                    onChange={(e) => {
                      setStartDate(e.target.value);
                      if (e.target.value !== "") {
                        setStartDateError(false);
                      } else {
                        setStartDateError(true);
                      }
                    }}
                  /> */}
                  <DatePicker
                    disabled={!isKhelcoachCourse() || moment().tz("Asia/Kolkata").isSameOrAfter(moment(startDate).tz("Asia/Kolkata"), "day")}
                    placeholderText="Select start date"
                    className={
                      startDateError
                        ? "my-custom-datepicker dateError"
                        : "my-custom-datepicker"
                    }
                    selected={startDate}
                    onChange={(date) => {
                      setEndDate("");
                      setStartDate(date);
                      if (date !== "") {
                        setStartDateError(false);
                      } else {
                        setStartDateError(true);
                      }
                    }}
                    minDate={formatDateToYYYYMMDD(moment().add(1, "days"))}
                    dateFormat="dd/MM/yyyy"
                  />
                  {startDateError && (
                    <FormErrorMessage>
                      Please select start date
                    </FormErrorMessage>
                  )}
                </FormControl>
                {/* Select Days */}
                <FormControl my={4} isInvalid={selectedDaysError}>
                  <FormLabel>Select Days</FormLabel>
                  <Stack spacing={5} direction="row">
                    {days.map((day, i) => (
                      <Button
                        key={i}
                        type="button"
                        disabled={!isKhelcoachCourse()}
                        onClick={() => {
                          setSelectedDays(
                            selectedDays.includes(day)
                              ? selectedDays.filter(
                                  (selected) => selected !== day
                                )
                              : [...selectedDays, day]
                          );
                          if (!day) {
                            setSelectedDaysError(true);
                          } else {
                            setSelectedDaysError(false);
                          }
                        }}
                        colorScheme={
                          selectedDays.includes(day) ? "telegram" : "gray"
                        }
                      >
                        {day}
                      </Button>
                    ))}
                  </Stack>
                  {selectedDaysError && (
                    <FormErrorMessage>Select the Days</FormErrorMessage>
                  )}
                </FormControl>
                <Divider />
                {/* End Date - radio btn */}
                <Box>
                  <FormLabel mt={3}>End Date</FormLabel>
                  <Flex
                    justifyContent={"flex-start"}
                    alignItems={"center"}
                    mb={3}
                  >
                    <Stack direction="row">
                      <Box mr={6}>
                        <input
                          id="horizontal-list-radio-license-never"
                          type="radio"
                          required
                          value=""
                          // defaultChecked={!isEnd}
                          checked={!isEnd}
                          onChange={(e) => {
                            e.target.checked ? setIsEnd(false) : setIsEnd(true);
                          }}
                          name="list-radio"
                          disabled={!isKhelcoachCourse()}
                        />
                        <label
                          htmlFor="horizontal-list-radio-license-never"
                          style={{
                            marginLeft: "8px",
                            cursor: "pointer",
                            fontSize: "1rem",
                            fontWeight: "600",
                          }}
                        >
                          Never
                        </label>
                      </Box>
                      <Box>
                        <input
                          id="horizontal-list-radio-license-on"
                          type="radio"
                          required
                          value=""
                          // defaultChecked={isEnd}
                          checked={isEnd}
                          onChange={(e) => {
                            e.target.checked ? setIsEnd(true) : setIsEnd(false);
                          }}
                          name="list-radio"
                          disabled={!isKhelcoachCourse()}
                        />
                        <label
                          htmlFor="horizontal-list-radio-license-on"
                          style={{
                            marginLeft: "8px",
                            cursor: "pointer",
                            fontSize: "1rem",
                            fontWeight: "600",
                          }}
                        >
                          On
                        </label>
                      </Box>
                    </Stack>
                  </Flex>
                  {/* when end date is on*/}
                  {isEnd && (
                    <FormControl my={3}>
                      <FormLabel>Select End Date</FormLabel>
                      <DatePicker
                        placeholderText="Select end date"
                        className={
                          startDateError
                            ? "my-custom-datepicker dateError"
                            : "my-custom-datepicker"
                        }
                        selected={endDate}
                        onChange={(date) => {
                          setEndDate(date);
                          if (isEnd) {
                            if (date !== "") {
                              setEndDateError(false);
                            } else {
                              setEndDateError(true);
                            }
                          }
                        }}
                        minDate={formatDateToYYYYMMDD(
                          startDate
                            ? new Date(startDate).setDate(
                                new Date(startDate).getDate() 
                              )
                            : new Date()
                        )}
                        dateFormat="dd/MM/yyyy"
                        disabled={!isKhelcoachCourse() || isStartDateInPastOrToday()}
                      />
                      {endDateError && (
                        <FormErrorMessage>Select end date</FormErrorMessage>
                      )}
                    </FormControl>
                  )}
                </Box>
                {/* Start Time && End time */}
                <Divider />
                <Flex justifyContent={"space-between"} alignItems={"center"}>
                  <FormControl
                    my={3}
                    flexBasis={"48%"}
                    isInvalid={timeFromError}
                  >
                    <FormLabel>Start Time</FormLabel>
                    <DatePicker
                      selected={timeFrom}
                      className={
                        timeFromError
                          ? "my-custom-datepicker dateError"
                          : "my-custom-datepicker"
                      }
                      onChange={async (value) => {
                        if (value === "") {
                          setTimeFromError(true);
                        } else {
                          setTimeFromError(false);
                        }
                        setTimeFrom(value);
                        setTimeTo("");
                      }}
                      showTimeSelect
                      showTimeSelectOnly
                      timeIntervals={15}
                      timeCaption="Time"
                      dateFormat="h:mm aa"
                      placeholderText="Select time"
                      disabled={!isKhelcoachCourse() || isStartDateInPastOrToday() || startDate === ""}
                      minTime={minTime || new Date(0, 0, 0, 0, 0)}
                      maxTime={new Date(0, 0, 0, 23, 59)}
                    />
                    {timeFromError && (
                      <FormErrorMessage>Select start time</FormErrorMessage>
                    )}
                  </FormControl>
                  <FormControl my={3} flexBasis={"48%"} isInvalid={timeToError}>
                    <FormLabel>End Time</FormLabel>
                    <DatePicker
                      disabled={!isKhelcoachCourse() || isStartDateInPastOrToday() || timeFrom === ""}
                      selected={timeTo}
                      onChange={async (value) => {
                        await getAvailableSlots(`${id}`, "", value);
                        setTimeTo(value);
                        if (value === "") {
                          setTimeToError(true);
                        } else {
                          setTimeToError(false);
                          // Check 30-minute gap validation
                          if (timeFrom && value) {
                            const startTimeMs = timeFrom.getTime();
                            const endTimeMs = value.getTime();
                            const timeDifference = endTimeMs - startTimeMs;
                            const thirtyMinutesInMs = 30 * 60 * 1000;

                            // Show error only if end time is after start time but less than 30 minutes
                            if (timeDifference > 0 && timeDifference < thirtyMinutesInMs) {
                              toast({
                                title: "Invalid time range",
                                description: "End time must be at least 30 minutes after start time",
                                status: "warning",
                                duration: 4000,
                                position: "top",
                                isClosable: true,
                              });
                            }
                          }
                        }
                      }}
                      showTimeSelect
                      showTimeSelectOnly
                      timeIntervals={15}
                      timeCaption="Time"
                      dateFormat="h:mm aa"
                      className={
                        timeToError
                          ? "my-custom-datepicker dateError"
                          : "my-custom-datepicker"
                      }
                      placeholderText="Select time"
                      minTime={getMinTime()}
                      maxTime={new Date(0, 0, 0, 23, 59)}
                    />
                    {timeToError && (
                      <FormErrorMessage>Select end time</FormErrorMessage>
                    )}
                  </FormControl>
                </Flex>
                {/* Conflicting Dates */}
                {showSlotConflict && (
                  <Box my={4}>
                    <Flex
                      justifyContent={"center"}
                      alignItems={"center"}
                      mb={2}
                    >
                      <Text
                        color={"red.500"}
                        fontSize={"lg"}
                        fontWeight={"semibold"}
                      >
                        Conflicting Courses
                      </Text>
                    </Flex>
                    <TableContainer>
                      <Table variant="simple">
                        <Thead
                          bgColor={"#c1eaee"}
                          position={"sticky"}
                          top={"0px"}
                        >
                          <Tr bgColor={"red.100"}>
                            <Th>Course Id</Th>
                            <Th>Start Date</Th>
                            <Th>End Date</Th>
                            <Th>Start Time</Th>
                            <Th>End Time</Th>
                            <Th>Days</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {conflictResult?.conflictingDates?.map(
                            (conflictDate, inx) => {
                              return (
                                <Tr bgColor={"gray.50"} key={inx}>
                                  <Td>{conflictDate?.courseId}</Td>
                                  <Td>
                                    {formatSlotDate(conflictDate?.startDate)}
                                  </Td>
                                  <Td>
                                    {formatSlotDate(conflictDate?.endDate)}
                                  </Td>
                                  <Td>{conflictDate?.conflictingStartTime}</Td>
                                  <Td>{conflictDate?.conflictingEndTime}</Td>
                                  <Td>
                                    <UnorderedList>
                                      {conflictDate?.conflictingDays?.map(
                                        (days, i) => {
                                          return (
                                            <ListItem key={i}>{days}</ListItem>
                                          );
                                        }
                                      )}
                                    </UnorderedList>
                                  </Td>
                                </Tr>
                              );
                            }
                          )}
                        </Tbody>
                      </Table>
                    </TableContainer>
                  </Box>
                )}
                {/* price */}
                {classType === "class" ? (
                  <>
                    <Flex
                      justifyContent={"space-between"}
                      alignItems={"center"}
                    >
                      <FormControl
                        my={4}
                        flexBasis={"30%"}
                        isInvalid={fees30Error}
                      >
                        <FormLabel>
                          Price <span>(30 minutes)</span>
                        </FormLabel>
                        <Input
                          type="number"
                          name="price"
                          id="price"
                          placeholder="₹0.00"
                          value={fees30}
                          autoComplete="off"
                          isReadOnly={!isKhelcoachCourse() || isStartDateInPastOrToday()}
                          onChange={(e) => {
                            let newValue = e.target.value;
                            newValue = newValue.replace(/[^\d.]/g, "");
                            newValue = newValue.replace(/^0+/, "");
                            newValue = newValue.replace(/^\./, "");
                            if (newValue.includes(".")) {
                              newValue = newValue.split(".")[0];
                            }
                            setFees30(newValue);
                            if (e.target.value === "" && !fees60) {
                              setFees30Error(true);
                            } else {
                              setFees60Error(false);
                              setFees30Error(false);
                            }
                          }}
                        />
                        {fees30Error && (
                          <FormErrorMessage>
                            Please enter the price
                          </FormErrorMessage>
                        )}
                      </FormControl>

                      <FormControl
                        my={4}
                        flexBasis={"30%"}
                        isInvalid={fees60Error}
                      >
                        <FormLabel>
                          Price <span>(60 minutes)</span>
                        </FormLabel>
                        <Input
                          type="number"
                          name="price"
                          id="price"
                          placeholder="₹0.00"
                          value={fees60}
                          autoComplete="off"
                          isReadOnly={!isKhelcoachCourse() || isStartDateInPastOrToday()}
                          onChange={(e) => {
                            let newValue = e.target.value;
                            newValue = newValue.replace(/^0+/, "");
                            newValue = newValue.replace(/^\./, "");
                            if (newValue.includes(".")) {
                              newValue = newValue.split(".")[0];
                            }
                            setFees60(newValue);
                            if (e.target.value === "" && !fees30) {
                              setFees60Error(true);
                            } else {
                              setFees60Error(false);
                              setFees30Error(false);
                            }
                          }}
                        />
                        {fees60Error && (
                          <FormErrorMessage>
                            Please enter the price
                          </FormErrorMessage>
                        )}
                      </FormControl>
                    </Flex>
                  </>
                ) : (
                  <FormControl my={4} isInvalid={priceError}>
                    <FormLabel>Price</FormLabel>
                    <Input
                      type="number"
                      name="price"
                      id="price"
                      placeholder="₹0.00"
                      value={price}
                      autoComplete="off"
                      isReadOnly={!isKhelcoachCourse() || isStartDateInPastOrToday()}
                      onChange={(e) => {
                        setPrice(e.target.value);
                        if (e.target.value !== "") {
                          setPriceError(false);
                        } else {
                          setPriceError(true);
                        }
                      }}
                    />
                    {priceError && (
                      <FormErrorMessage>
                        Please enter the price
                      </FormErrorMessage>
                    )}
                  </FormControl>
                )}
              </CardBody>
            </Card>
            <Card
              mt={4}
              pointerEvents={getPointerEvents()}
            >
              <CardBody>
                {/* Session type if -- Course */}
                {classType === "course" && (
                  <FormControl mt={6}>
                    <FormLabel>Session Type</FormLabel>
                    <Select
                      value={sessionType}
                      onChange={(e) => {
                        setSessionType(e.target.value);
                        setMaximumGroupSize(1);
                      }}
                    >
                      <option value="group">Group</option>
                      <option value="individual">Individual</option>
                    </Select>
                  </FormControl>
                )}

                {/* Is it a camp if --- Course */}
                {classType !== "class" && (
                  <>
                    <Flex
                      justifyContent={"flex-start"}
                      alignItems={"center"}
                      mt={4}
                      mb={2}
                    >
                      <Text
                        fontWeight={"semibold"}
                        fontSize={"16px"}
                        mb={1}
                        mr={4}
                      >
                        Is it a camp ?{" "}
                      </Text>
                      <Box mr={4}>
                        <input
                          type="radio"
                          id="yes"
                          name="camp-type"
                          value="yes"
                          onChange={handleRadioChange}
                          style={{ marginTop: "2px" }}
                        />
                        <label
                          htmlFor="yes"
                          style={{ marginLeft: "8px", cursor: "pointer" }}
                        >
                          Yes
                        </label>
                      </Box>
                      <Box>
                        <input
                          type="radio"
                          id="no"
                          name="camp-type"
                          value="no"
                          onChange={handleRadioChange}
                          defaultChecked
                          style={{ marginTop: "2px" }}
                        />
                        <label
                          htmlFor="no"
                          style={{ marginLeft: "8px", cursor: "pointer" }}
                        >
                          No
                        </label>
                      </Box>
                    </Flex>
                    {isItCamp && (
                      <Box>
                        <Input
                          type="text"
                          name="camp-name"
                          id="camp-name"
                          value={campName}
                          autoComplete="off"
                          placeholder="Enter camp name"
                          onChange={(e) => {
                            setCampName(e.target.value);
                            if (e.target.value !== "") {
                              setCampNameError(false);
                            } else {
                              setCampNameError(true);
                            }
                            if (
                              e.target.value.length > 100 ||
                              e.target.value.length < 3
                            ) {
                              setCampNameError(true);
                            } else {
                              setCampNameError(false);
                            }
                          }}
                        />
                        {campNameError && (
                          <FormErrorMessage>
                            Please enter camp name characters must be greater
                            than or equal to 3 and less than equal to 100
                          </FormErrorMessage>
                        )}
                      </Box>
                    )}
                  </>
                )}

                {/* Category */}
                <FormControl mt={6} isInvalid={categoryError}>
                  <FormLabel>Category</FormLabel>
                  <Select
                    placeholder="Select Category"
                    value={categoryType}
                    onChange={(e) => {
                      setCategoryType(e.target.value);
                      setCategoryError(false);
                    }}
                    disabled={!isKhelcoachCourse()}
                  >
                    {categories?.map((category, inx) => (
                      <option key={inx} value={category?.name}>
                        {category?.name}
                      </option>
                    ))}
                  </Select>
                  {categoryError && (
                    <FormErrorMessage>Please select category</FormErrorMessage>
                  )}
                </FormControl>
                {/* Select Facility */}
                <FormControl mt={6} isInvalid={facilityError}>
                  <FormLabel>Facility</FormLabel>
                  <Select
                    placeholder="Select Facility"
                    value={courseData?.facility?.name}
                    onChange={(e) => {
                      setCourseFacility(e?.target?.value);
                      setFacilityError(false);
                      setCourseAmeneties(
                        facilities.filter((x) => x.name === e.target.value)[0]
                          .amenities
                      );
                    }}
                    disabled={!isKhelcoachCourse()}
                  >
                    {facilities.map((facility, inx) => (
                      <option key={inx} value={facility.name}>
                        {facility.name}
                      </option>
                    ))}
                  </Select>
                  {facilityError && (
                    <FormErrorMessage>Please select facility</FormErrorMessage>
                  )}
                </FormControl>

                {/* Max group size */}
                {sessionType === "group" && classType === "course" && (
                  <FormControl mt={6} isInvalid={maximumGroupSizeError}>
                    <FormLabel>Maximum Group Size</FormLabel>
                    <Input
                      type="number"
                      placeholder="Enter maximum group size"
                      name="max-group-size"
                      id="max-group-size"
                      value={maximumGroupSize}
                      onChange={(e) => {
                        setMaximumGroupSize(
                          e.target.value.replace(/\s+/g, " ").trim()
                        );
                        if (e.target.value !== "" && e.target.value !== "0") {
                          setMaximumGroupSizeError(false);
                        } else {
                          setMaximumGroupSizeError(true);
                        }
                        if (Number(e.target.value) > 500) {
                          setMaximumGroupSizeError(true);
                        } else {
                          setMaximumGroupSizeError(false);
                        }
                      }}
                      disabled={!isKhelcoachCourse()}
                    />
                    {maximumGroupSizeError && (
                      <FormErrorMessage>
                        Please enter maximum group size, must be greater than 0
                        and less than equal to 500
                      </FormErrorMessage>
                    )}
                  </FormControl>
                )}
                {/* Proficiency Level */}
                <FormControl mt={6}>
                  <FormLabel>Proficiency Level</FormLabel>
                  <Menu>
                    <MenuButton
                      as={Button}
                      w={"100%"}
                      rightIcon={<FaChevronDown />}
                    >
                      Select Proficiency
                    </MenuButton>
                    <MenuList minW={window.innerWidth - 600 + "px"}>
                      {proficiencyLevelOption?.map((proficiency, i) => (
                        <Box p={2} key={i}>
                          <Checkbox
                            size="md"
                            id={i}
                            colorScheme="green"
                            value={proficiency.value} // Set the value to the proficiency itself
                            isChecked={proficiencyLevel.includes(
                              proficiency.value
                            )}
                            onChange={(e) => {
                              const { checked, value } = e.target;
                              setProficiencyLevel((prevProficiencyLevel) => {
                                if (checked) {
                                  // Add the proficiency value if checked
                                  return [...prevProficiencyLevel, value];
                                } else {
                                  // Remove the proficiency value if unchecked
                                  return prevProficiencyLevel.filter(
                                    (item) => item !== value
                                  );
                                }
                              });
                            }}
                            isDisabled={!isKhelcoachCourse()}
                          >
                            {proficiency.name}
                          </Checkbox>
                        </Box>
                      ))}
                    </MenuList>
                  </Menu>
                  <HStack spacing={4} mt={2}>
                    {proficiencyLevel?.map((proficiency, i) => (
                      <Tag
                        size={"md"}
                        key={i}
                        variant="solid"
                        colorScheme="teal"
                      >
                        {proficiency}
                      </Tag>
                    ))}
                  </HStack>
                  {categoryError && (
                    <Text color={"red.500"} fontSize={"sm"} mt={1}>
                      Categories are required
                    </Text>
                  )}
                </FormControl>
                {/* Ameneties */}
                <FormControl mt={6}>
                  <FormLabel>Ameneties</FormLabel>
                  <ReactQuill
                    theme="snow"
                    value={courseAmeneties}
                    onChange={(value) => {
                      setCourseAmeneties(value);
                    }}
                  />
                </FormControl>
                {/* Thing Have to Carry with */}
                <FormControl mt={6}>
                  <FormLabel>Thing Have to Carry with</FormLabel>
                  <ReactQuill
                    theme="snow"
                    value={carryThings}
                    onChange={(value) => {
                      setCarryThings(value);
                    }}
                  />
                </FormControl>
                {/* Cancellation Policy */}
                <FormControl mt={6}>
                  <FormLabel>Cancellation Policy</FormLabel>
                  <ReactQuill
                    theme="snow"
                    value={cancellationPolicy}
                    onChange={(value) => {
                      setCancellationPolicy(value);
                    }}
                  />
                </FormControl>
              </CardBody>
            </Card>
            {userData?.accessScopes?.course?.includes("write") && isKhelcoachCourse() && !isStartDateInPastOrToday() && (
              <Flex
                mt={4}
                justifyContent={"space-between"}
                alignItems={"center"}
              >
                <Button
                  variant={"solid"}
                  colorScheme="red"
                  size={"sm"}
                  px={8}
                  flexBasis={"49%"}
                  onClick={onOpen}
                >
                  Discard
                </Button>
                <Button
                  variant={"solid"}
                  colorScheme="green"
                  size={"sm"}
                  px={8}
                  ml={4}
                  isLoading={btnIsLoading}
                  onClick={saveHandler}
                  flexBasis={"49%"}
                >
                  Update
                </Button>
              </Flex>
            )}
          </CardBody>

          <AlertDialog isOpen={isOpen} onClose={onClose}>
            <AlertDialogOverlay>
              <AlertDialogContent>
                <AlertDialogHeader fontSize="lg" fontWeight="bold">
                  Discard Changes
                </AlertDialogHeader>

                <AlertDialogBody isCentered>
                  Are you sure? You can't undo this action afterwards.
                </AlertDialogBody>

                <AlertDialogFooter>
                  <Button onClick={onClose}>No</Button>
                  <Button
                    colorScheme="red"
                    onClick={() => {
                      onClose();
                      renderMe();
                    }}
                    ml={3}
                  >
                    Yes
                  </Button>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialogOverlay>
          </AlertDialog>
        </Card>
      </Layout>
    </Box>
  );
};

export default CourseUpdate;
