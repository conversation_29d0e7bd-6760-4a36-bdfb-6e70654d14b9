import { useState, useEffect } from "react";
import { useToast } from "@chakra-ui/react";
import { useSelector } from "react-redux";
import axios from "axios";

export const useNewsUpdates = (onModalClose) => {
  const [newsUpdates, setNewsUpdates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    title: "",
    image: "",
    date: "",
    link: "",
    position: ""
  });
  const [editingId, setEditingId] = useState(null);
  const [deleteId, setDeleteId] = useState(null);
  const [imageFile, setImageFile] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [adjustBtnEdit, setAdjustBtnEdit] = useState(false);
  const [previousNewsData, setPreviousNewsData] = useState([]);
  const [posBtnLoading, setPosBtnLoading] = useState(false);
  const [draggedItemIndex, setDraggedItemIndex] = useState(null);

  const toast = useToast();
  const userData = useSelector((state) => state.user);
  const token = sessionStorage.getItem("admintoken")?.split(" ")[1];

  const fetchNewsUpdates = async () => {
    try {
      setLoading(true);
      const response = await axios.get(
        `${process.env.REACT_APP_BASE_URL}/api/cms/news-updates`,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setNewsUpdates(response.data?.sort((a, b) => a.position - b.position) || []);
    } catch (error) {
      toast({
        title: "Error fetching news updates",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const uploadImage = async (file) => {
    const formData = new FormData();
    formData.append("image", file);
    const response = await axios.post(
      `${process.env.REACT_APP_BASE_URL}/api/cms/upload`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data.url;
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      let imageUrl = formData.image;
      if (imageFile) {
        imageUrl = await uploadImage(imageFile);
      }

      const payload = {
        ...formData,
        image: imageUrl,
        position: parseInt(formData.position) || (newsUpdates.length + 1)
      };

      if (editingId) {
        await axios.patch(
          `${process.env.REACT_APP_BASE_URL}/api/cms/update/news-update/${editingId}`,
          payload,
          { headers: { Authorization: `Bearer ${token}` } }
        );
        toast({
          title: "News update updated successfully",
          status: "success",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
      } else {
        await axios.post(
          `${process.env.REACT_APP_BASE_URL}/api/cms/create/news-update`,
          payload,
          { headers: { Authorization: `Bearer ${token}` } }
        );
        toast({
          title: "News update created successfully",
          status: "success",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
      }

      handleCloseModal();
      if (onModalClose) onModalClose(); // Close the modal
      fetchNewsUpdates();
    } catch (error) {
      toast({
        title: "Error saving news update",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (item) => {
    setFormData({
      title: item.title,
      image: item.image,
      date: item.date?.split('T')[0] || '',
      link: item.link,
      position: item.position.toString()
    });
    setEditingId(item._id);
    setImageFile(null);
  };

  const handleDelete = async () => {
    try {
      await axios.delete(
        `${process.env.REACT_APP_BASE_URL}/api/cms/delete/news-update/${deleteId}`,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      toast({
        title: "News update deleted successfully",
        status: "success",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
      fetchNewsUpdates();
    } catch (error) {
      toast({
        title: "Error deleting news update",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
  };

  const updateNewsPosition = () => {
    setPosBtnLoading(true);
    const value = newsUpdates.map((x) => ({ id: x._id, newPosition: x.position }));
    
    axios.patch(
      `${process.env.REACT_APP_BASE_URL}/api/cms/update/news-update/position`,
      { updates: value },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    )
    .then(() => {
      setPosBtnLoading(false);
      setPreviousNewsData([]);
      setAdjustBtnEdit(false);
      toast({
        title: "Position updated",
        status: "success",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    })
    .catch((error) => {
      setPosBtnLoading(false);
      setPreviousNewsData([]);
      setAdjustBtnEdit(false);
      toast({
        title: error.response?.status === 403 
          ? "You don't have access to perform this action"
          : "Something went wrong please try again later",
        status: error.response?.status === 403 ? "warning" : "error",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    });
  };

  const handleCloseModal = () => {
    setFormData({ title: "", image: "", date: "", link: "", position: "" });
    setEditingId(null);
    setImageFile(null);
  };

  const handleDragStart = (index) => setDraggedItemIndex(index);

  const handleDragOver = (index) => {
    if (draggedItemIndex === null) return;
    const newItems = [...newsUpdates];
    const draggedItem = newItems[draggedItemIndex];
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);
    newItems.forEach((item, idx) => { item.position = idx + 1; });
    setNewsUpdates(newItems);
    setDraggedItemIndex(index);
  };

  const handleDragEnd = () => setDraggedItemIndex(null);

  useEffect(() => {
    fetchNewsUpdates();
  }, []);

  return {
    newsUpdates, setNewsUpdates, loading, formData, setFormData, editingId,
    deleteId, setDeleteId, imageFile, setImageFile, submitting, adjustBtnEdit,
    setAdjustBtnEdit, previousNewsData, setPreviousNewsData, posBtnLoading,
    userData, handleSubmit, handleEdit, handleDelete, updateNewsPosition,
    handleCloseModal, handleDragStart, handleDragOver, handleDragEnd
  };
};