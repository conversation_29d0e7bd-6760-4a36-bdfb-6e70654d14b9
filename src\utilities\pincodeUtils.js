/**
 * Utility functions for handling pincode operations with Google Maps API
 */

/**
 * Parse Google Maps Geocoding API response to extract city, state, and country
 * @param {Array} results - Results array from Google Maps Geocoding API response
 * @returns {Object|null} - Object containing city, state, country or null if parsing fails
 */
export const parseGoogleMapsResponse = (results) => {
  if (!results || results.length === 0) return null;

  const addressComponents = results[0].address_components;
  let city = "";
  let state = "";
  let country = "";

  addressComponents.forEach(component => {
    const types = component.types;
    
    // Extract city (locality)
    if (types.includes("locality") && types.includes("political")) {
      city = component.long_name;
    }
    
    // Extract state (administrative_area_level_1)
    if (types.includes("administrative_area_level_1") && types.includes("political")) {
      state = component.long_name;
    }
    
    // Extract country
    if (types.includes("country") && types.includes("political")) {
      country = component.long_name;
    }
  });

  return { city, state, country };
};

/**
 * Validate pincode format (6 digits)
 * @param {string} pincode - Pincode to validate
 * @returns {boolean} - True if valid, false otherwise
 */
export const isValidPincode = (pincode) => {
  return /^\d{6}$/.test(pincode);
};

/**
 * Check if Google Maps API response indicates success
 * @param {Object} response - Google Maps API response
 * @returns {boolean} - True if response is successful, false otherwise
 */
export const isGoogleMapsResponseValid = (response) => {
  return !(
    response?.data?.status === "ZERO_RESULTS" ||
    response?.data?.status === "INVALID_REQUEST" ||
    !response?.data?.results ||
    response?.data?.results.length === 0
  );
};
