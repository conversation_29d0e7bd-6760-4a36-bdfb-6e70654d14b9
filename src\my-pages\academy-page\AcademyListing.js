import { <PERSON>, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import Layout from "../../layout/default";
import ReactPaginate from "react-paginate";
import "../../style/pagination.css";
import { FaAngleDown } from "react-icons/fa";
import { FaCircleArrowRight } from "react-icons/fa6";

import {
  Button,
  Card,
  TableContainer,
  Table,
  Thead,
  Badge,
  Tr,
  Th,
  Tbody,
  Td,
  Spinner,
  Flex,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Text,
  Input,
  Box,
  Tooltip,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useToast,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
  InputGroup,
  InputRightAddon,
  Select,
} from "@chakra-ui/react";
import axios from "axios";
import { useSelector } from "react-redux";
import { IoIosClose, IoMdSearch } from "react-icons/io";
import { IoFilter } from "react-icons/io5";

const AcademyListing = () => {
  const [academyDetails, setAcademyDetails] = useState({
    result: [],
    isLoading: false,
    error: false,
    notFound: false,
  });

  const [academyStatusChange, setAcademyStatusChange] = useState({
    type: "",
    id: "",
  });

  const [academyAuthStatusChange, setAcademyAuthStatusChange] = useState({
    type: "",
    id: "",
  });

  const [showSearch, setShowSearch] = useState(false);
  const [searchCoachName, setSearchCoachName] = useState("");
  const [selectedAuthStatus, setSelectedAuthStatus] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);

  const [renderMe, setRenderMe] = useState(0);

  const [isOpen3, setIsOpen3] = useState(false);
  const onClose3 = () => setIsOpen3(false);
  const onOpen3 = () => setIsOpen3(true);

  const [isOpen4, setIsOpen4] = useState(false);
  const onClose4 = () => setIsOpen4(false);
  const onOpen4 = () => setIsOpen4(true);

  const navigate = useNavigate();
  const toast = useToast();
  const token = sessionStorage?.getItem("admintoken")?.split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getAcademies = async (searchAcademyName, status, authStatus) => {
    setAcademyDetails({
      result: [],
      isLoading: true,
      error: false,
      notFound: false,
    });

    let queryString = "?";

    if (searchAcademyName) {
      queryString += `name=${searchAcademyName}`;
    } else {
      queryString = `?page=${currentPage}`;
    }

    if (status) {
      queryString += `${queryString ? "&" : ""}status=${status}`;
    }

    if (authStatus) {
      queryString += `${queryString ? "&" : ""}authStatus=${authStatus}`;
    }

    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy/${
        queryString ? `${queryString}` : ""
      }`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setAcademyDetails({
          result: response.data.data.academies,
          isLoading: false,
          error: false,
          notFound: response.data.data.academies.length === 0 ? true : false,
        });
        setTotalPages(Math.ceil(response.data.data.totalResults / 25));
      })
      .catch((error) => {
        console.log(error);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response.status === 401) {
          toast({
            title: "Session expired, please login",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
        setAcademyDetails({
          result: [],
          isLoading: false,
          error: true,
          notFound: false,
        });
      });
  };

  const changeAcademyStatus = () => {
    let data = JSON.stringify({
      status: academyStatusChange?.type,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy/updateStatus/${academyStatusChange?.id}`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setRenderMe((prev) => prev + 1);
        setAcademyStatusChange({ type: "", id: "" });
        onClose3();
        toast({
          title: "Academy status updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setAcademyStatusChange({ type: "", id: "" });
        onClose3();
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const updateAcademyAuthStatus = () => {
    let data = JSON.stringify({
      authStatus: academyAuthStatusChange?.type,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy/updateAuthStatus/${academyAuthStatusChange?.id}`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setRenderMe((prev) => prev + 1);
        setAcademyAuthStatusChange({ type: "", id: "" });
        onClose4();
        toast({
          title: "Academy Authorized",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setAcademyAuthStatusChange({ type: "", id: "" });
        onClose4();
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handlePageChange = ({ selected }) => {
    setCurrentPage(selected + 1);
  };

  useEffect(() => {
    getAcademies(searchCoachName, selectedStatus, selectedAuthStatus);
  }, [
    renderMe,
    currentPage,
    selectedAuthStatus,
    selectedStatus,
    searchCoachName,
  ]);

  return (
    <Layout title="Academy" content="container">
      <Flex
        w={"100%"}
        justifyContent={"space-between"}
        alignItems={"center"}
        mb={6}
      >
        {showSearch ? (
          <Box flexBasis={"58%"}>
            <InputGroup size="md">
              <Input
                pr="4.5rem"
                type="text"
                placeholder="Search"
                borderColor={"gray.300"}
                onChange={(e) => {
                  if (e.target.value.length >= 3) {
                    setTimeout(() => {
                      setSearchCoachName(e.target.value);
                    }, 500);
                  }
                  if (e.target.value.length === 0) {
                    setSearchCoachName("");
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    if (e.target.value.length >= 3) {
                      setSearchCoachName(e.target.value);
                    }
                  }
                }}
              />
              <InputRightAddon
                bgColor={"gray.300"}
                border={"1px"}
                borderColor={"gray.300"}
                onClick={() => {
                  setShowSearch(false);
                  setSearchCoachName("");
                }}
                cursor={"pointer"}
              >
                <IoIosClose fontSize={"24px"} />
              </InputRightAddon>
            </InputGroup>
          </Box>
        ) : (
          <Flex
            flexBasis={"59%"}
            justifyContent={"space-between"}
            alignItems={"center"}
          >
            <Breadcrumb fontWeight="medium" fontSize="sm">
              <BreadcrumbItem>
                <Link to="/">Dashboard</Link>
              </BreadcrumbItem>

              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Academy</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
            <Text
              display={"flex"}
              px={4}
              justifyContent={"center"}
              alignItems={"center"}
              py={"7px"}
              border={"1px"}
              borderColor={"gray.300"}
              rounded={"md"}
              color="gray.500"
              cursor={"pointer"}
              onClick={() => setShowSearch(true)}
            >
              <IoMdSearch fontSize={"24px"} />
              <IoFilter fontSize={"22px"} ml={1} />
            </Text>
          </Flex>
        )}
        <Flex flexBasis={"40%"} justifyContent={"space-between"}>
          <Box flexBasis={"31%"}>
            <Select
              placeholder="Status"
              borderColor={"gray.300"}
              cursor={"pointer"}
              bgColor={selectedStatus && "gray.300"}
              onChange={(e) => {
                setSelectedStatus(e.target.value);
                setCurrentPage(1);
              }}
            >
              <option value="">All</option>
              <option value="active">Active</option>
              <option value="inactive">In-Active</option>
            </Select>
          </Box>
          <Box flexBasis={"33%"}>
            <Select
              placeholder="Auth Status"
              borderColor={"gray.300"}
              cursor={"pointer"}
              bgColor={selectedAuthStatus && "gray.300"}
              onChange={(e) => {
                setSelectedAuthStatus(e.target.value);
                setCurrentPage(1);
              }}
            >
              <option value="">All</option>
              <option value="authorized">Authorized</option>
              <option value="unauthorized">Unauthorized</option>
            </Select>
          </Box>

          <Box flexBasis={"31%"}>
            <Link to={"/academy-page/creation"}>
              <Button
                variant={"outline"}
                colorScheme="teal"
                size={"sm"}
                py={5}
                px={4}
                ml={3}
                isDisabled={!userData?.accessScopes?.academy?.includes("write")}
              >
                Add Academy
              </Button>
            </Link>
          </Box>
        </Flex>
      </Flex>

      <Card>
        {!academyDetails?.isLoading && academyDetails?.error ? (
          <Flex
            justifyContent={"center"}
            alignItems={"center"}
            w={"full"}
            my={10}
          >
            <Text color={"red.500"}>
              Something went wrong please try again later...
            </Text>
          </Flex>
        ) : (
          <TableContainer
            height={`${window.innerHeight - 238}px`}
            overflowY={"scroll"}
          >
            <Table variant="simple">
              <Thead
                bgColor={"#c1eaee"}
                position={"sticky"}
                top={"0px"}
                zIndex={"99"}
              >
                <Tr bgColor={"#E2DFDF"}>
                  <Th>Name</Th>
                  <Th>Mobile</Th>
                  <Th>Email</Th>
                  <Th>Platform Fee (%)</Th>
                  <Th>Status</Th>
                  <Th>Auth Status</Th>
                  <Th>Action</Th>
                </Tr>
              </Thead>
              <Tbody>
                {academyDetails?.isLoading && !academyDetails?.error ? (
                  <Tr>
                    <Td></Td>
                    <Td></Td>
                    <Td
                      display={"flex"}
                      justifyContent={"flex-end"}
                      alignItems={"center"}
                    >
                      <Spinner />
                    </Td>
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                  </Tr>
                ) : !academyDetails?.isLoading && academyDetails?.error ? (
                  <Flex
                    justifyContent={"center"}
                    alignItems={"center"}
                    w={"full"}
                    my={10}
                  >
                    <Text color={"red.500"}>
                      Something went wrong please try again later...
                    </Text>
                  </Flex>
                ) : !academyDetails?.notFound ? (
                  academyDetails?.result?.map((academy, i) => {
                    return (
                      <Tr key={i}>
                        <Td
                          style={{
                            whiteSpace: "pre-wrap",
                            wordWrap: "break-word",
                          }}
                          fontSize={"14px"}
                        >
                          {academy?.name}
                        </Td>
                        <Td fontSize={"14px"}>{academy?.mobile}</Td>
                        <Td fontSize={"14px"}>{academy?.email}</Td>
                        <Td fontSize={"14px"}>{academy?.platformFee}</Td>
                     <Td fontSize={"14px"}>
  {userData?.accessScopes?.academy?.includes("write") && (
    <Menu>
      <Tooltip
        label={
          academy?.authStatus !== "authorized" &&
          "Academy must be authorized to change status"
        }
      >
        <MenuButton
          variant={"outline"}
          as={Button}
          size={"xs"}
          isDisabled={academy?.authStatus !== "authorized"}
          py={3}
          colorScheme={
            academy?.status === "active"
              ? "green"
              : "red"
          }
          rightIcon={<FaAngleDown />}
        >
          {academy?.status?.toUpperCase()}
        </MenuButton>
      </Tooltip>
      <MenuList>
        <MenuItem
          isDisabled={academy?.status === "active"}
          onClick={() => {
            setAcademyStatusChange({
              type: "active",
              id: academy?._id,
            });
            onOpen3();
          }}
        >
          Active
        </MenuItem>
        <MenuItem
          isDisabled={academy?.status !== "active"}
          onClick={() => {
            setAcademyStatusChange({
              type: "inactive",
              id: academy?._id,
            });
            onOpen3();
          }}
        >
          Inactive
        </MenuItem>
      </MenuList>
    </Menu>
  )}
  {!userData?.accessScopes?.academy?.includes("write") && (
    <Badge colorScheme={academy?.status === "active" ? "green" : "red"}>
      {academy?.status?.toUpperCase()}
    </Badge>
  )}
</Td>
                        {academy?.authStatus === "authorized" ? (
                          <Td fontSize={"14px"}>
                            <Badge colorScheme="green" textAlign={"center"}>
                              Authorized
                            </Badge>
                          </Td>
                        ) : (
                          <Td fontSize={"14px"}>
                            {userData?.accessScopes?.academy?.includes(
                              "write"
                            ) && (
                              <Menu>
                                <MenuButton
                                  variant={"outline"}
                                  as={Button}
                                  size={"xs"}
                                  py={3}
                                  colorScheme={
                                    academy?.authStatus === "authorized"
                                      ? "green"
                                      : "red"
                                  }
                                  rightIcon={<FaAngleDown />}
                                  // isDisabled={academy?.status !== "active"}
                                >
                                  {academy?.authStatus?.toUpperCase()}
                                </MenuButton>
                                <MenuList>
                                  <MenuItem
                                    isDisabled={
                                      academy?.authStatus === "authorized"
                                    }
                                    onClick={() => {
                                      setAcademyAuthStatusChange({
                                        type: "authorized",
                                        id: academy?._id,
                                      });
                                      onOpen4();
                                    }}
                                  >
                                    Authorized
                                  </MenuItem>
                                  {/* <MenuItem
                                    isDisabled={
                                      academy?.authStatus !== "authorized"
                                    }
                                    onClick={() => {
                                      setAcademyAuthStatusChange({
                                        type: "unauthorized",
                                        id: academy?._id,
                                      });
                                      onOpen4();
                                    }}
                                  >
                                    Unauthorized
                                  </MenuItem> */}
                                </MenuList>
                              </Menu>
                            )}
                          </Td>
                        )}

                        <Td textAlign={"center"}>
                          {userData?.accessScopes?.academy?.includes(
                            "read"
                          ) && (
                            <Tooltip label="Academy Details">
                              <Text
                                as={"span"}
                                fontSize={"22px"}
                                cursor={"pointer"}
                                onClick={() =>
                                  navigate(
                                    `/academy-page/details/${academy?._id}`
                                  )
                                }
                              >
                                <FaCircleArrowRight />
                              </Text>
                            </Tooltip>
                          )}
                        </Td>
                      </Tr>
                    );
                  })
                ) : (
                  <Tr>
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                    <Td
                      display={"flex"}
                      justifyContent={"flex-start"}
                      alignItems={"center"}
                    >
                      <Text color={"green.500"} fontWeight={"semibold"}>
                        No result found
                      </Text>
                    </Td>
                    <Td></Td>
                    <Td></Td>
                  </Tr>
                )}
              </Tbody>
            </Table>
          </TableContainer>
        )}
      </Card>

      {/* Pagination */}
      {!academyDetails?.notFound && (
        <Flex
          justifyContent="center"
          alignItems="center"
          flexDirection={"row"}
          w={"100%"}
          mt={5}
        >
          <ReactPaginate
            previousLabel="Previous"
            nextLabel="Next"
            breakLabel="..."
            pageCount={totalPages}
            marginPagesDisplayed={2}
            pageRangeDisplayed={5}
            onPageChange={handlePageChange}
            containerClassName="pagination"
            activeClassName="active"
            forcePage={currentPage - 1}
          />
        </Flex>
      )}

      {/* Status - alert */}
      <AlertDialog isOpen={isOpen3} onClose={onClose3} isCentered>
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Update Academy Status
            </AlertDialogHeader>

            <AlertDialogBody>
              When updating the academy status, an automatic email will be
              triggered to notify academy the relevant changes
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button
                colorScheme="red"
                onClick={() => {
                  onClose3();
                  setAcademyStatusChange({ type: "", id: "" });
                }}
              >
                Cancel
              </Button>
              <Button
                colorScheme="green"
                onClick={() => {
                  changeAcademyStatus();
                }}
                ml={3}
              >
                Save Changes
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
      {/* Auth Status - alert */}
      <AlertDialog isOpen={isOpen4} onClose={onClose4} isCentered>
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Update Academy Auth Status
            </AlertDialogHeader>

            <AlertDialogBody>
              When updating the Auth status, an automatic email will be
              triggered to notify academy the relevant changes
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button
                colorScheme="red"
                onClick={() => {
                  onClose4();
                  setAcademyAuthStatusChange({ type: "", id: "" });
                }}
              >
                Cancel
              </Button>
              <Button
                colorScheme="green"
                onClick={() => {
                  updateAcademyAuthStatus();
                }}
                ml={3}
              >
                Save Changes
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Layout>
  );
};

export default AcademyListing;
