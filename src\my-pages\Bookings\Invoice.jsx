import React from "react";
import { useEffect, useState } from "react";
import { State } from "country-state-city";

const Invoice = ({ bookingIds }) => {
  const [thankyouDetails, setThankyouDetails] = useState();
  const [booking_id, setBooking_Id] = useState(bookingIds);
  const [playerHomeState, setPlayerHomeState] = useState("");
  const [registeredState, setRegisteredState] = useState("Delhi");
  const countryCode = 'IN';
  const token = sessionStorage.getItem("admintoken").split(" ")[1];

  useEffect(() => {
    if (bookingIds) {
      setBooking_Id(bookingIds);
    }
  }, [bookingIds]);

  useEffect(() => {
    if (booking_id) {
      getBookingById(booking_id);
    }
  }, [booking_id]);

  async function getBookingById(booking_id) {
    const requestOptions = {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      redirect: "follow",
    };

    try {
      const response = await fetch(
        `${process.env.REACT_APP_BASE_URL}/api/booking/${booking_id}`,
        requestOptions
      );
      const result = await response.json();

      if (result) {
        setThankyouDetails(result);

        // Convert GST state ISO code to full state name
        const homeState = State.getStateByCodeAndCountry(
          result?.player?.homeState,
          "IN"
        )?.name || "";

        setPlayerHomeState(homeState);
      }
    } catch (error) {
      console.error("Error fetching booking:", error);
    }
  }

  const handlePrint = () => {
    const printContents = document.getElementById('invoice-content').innerHTML;
    const printWindow = window.open('', '', 'height=800,width=1000');
    printWindow.document.write('<html><head><title>Invoice</title>');
    printWindow.document.write('<style>');
    printWindow.document.write(`
      body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; }
      .invoice-container { max-width: 1000px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border-radius: 8px; overflow: hidden; }
      .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 50px 40px; text-align: center; position: relative; }
      .header-overlay { position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255,255,255,0.1); clip-path: polygon(0 0, 100% 0, 85% 100%, 0% 100%); }
      .header h1 { font-size: 3rem; font-weight: 700; margin: 0; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); position: relative; z-index: 1; }
      .header p { font-size: 1.3rem; margin: 15px 0 0 0; opacity: 0.9; position: relative; z-index: 1; }
      .company-details { padding: 40px; display: grid; grid-template-columns: 1fr 1fr; gap: 50px; border-bottom: 2px solid #f0f0f0; }
      .company-info h3 { color: #2d3748; font-size: 1.4rem; font-weight: 600; margin: 0 0 20px 0; }
      .company-info div { color: #4a5568; line-height: 1.8; }
      .company-info p { margin: 8px 0; }
      .invoice-details { text-align: right; }
      .invoice-details-box { display: inline-block; text-align: left; background-color: #f7fafc; padding: 25px; border-radius: 8px; border: 1px solid #e2e8f0; }
      .invoice-details-row { margin-bottom: 20px; }
      .invoice-details-row span:first-child { color: #2d3748; font-weight: 600; display: inline-block; width: 120px; }
      .invoice-details-row span:last-child { color: #4a5568; font-weight: 500; }
      .bill-to { padding: 40px; border-bottom: 2px solid #f0f0f0; }
      .bill-to h3 { color: #2d3748; font-size: 1.4rem; font-weight: 600; margin: 0 0 20px 0; }
      .bill-to-box { background-color: #f7fafc; padding: 25px; border-radius: 8px; border: 1px solid #e2e8f0; }
      .bill-to-box p:first-child { color: #2d3748; font-weight: 600; margin: 0 0 12px 0; font-size: 1.2rem; }
      .bill-to-box p { color: #4a5568; margin: 8px 0; font-size: 1rem; }
      .invoice-items { padding: 40px; }
      .items-header { background-color: #2d3748; color: white; padding: 20px 25px; border-radius: 8px 8px 0 0; font-weight: 600; font-size: 1.2rem; }
      .items-table { border: 1px solid #e2e8f0; border-radius: 0 0 8px 8px; overflow: hidden; }
      .items-table table { width: 100%; border-collapse: collapse; }
      .items-table thead tr { background-color: #f7fafc; border-bottom: 2px solid #e2e8f0; }
      .items-table th { padding: 20px 25px; text-align: left; color: #2d3748; font-weight: 600; font-size: 1rem; }
      .items-table th:last-child { text-align: right; }
      .items-table td { padding: 20px 25px; color: #4a5568; font-size: 1rem; border-bottom: 1px solid #e2e8f0; }
      .items-table td:last-child { text-align: right; font-weight: 500; }
      .items-table tr:last-child { background-color: #2d3748; color: white; }
      .items-table tr:last-child td { padding: 25px; font-weight: 700; font-size: 1.2rem; }
      .footer { padding: 30px 40px; background-color: #f7fafc; border-top: 2px solid #e2e8f0; text-align: center; color: #4a5568; font-size: 1rem; }
      .print-button { display: none; }
      @media print { .print-button { display: none !important; } }
    `);
    printWindow.document.write('</style>');
    printWindow.document.write('</head><body>');
    printWindow.document.write(printContents);
    printWindow.document.write('</body></html>');
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 500);
  };

  // amount breakdown
  let subtotal = thankyouDetails?.classes?.reduce(
    (accu, x) => accu + x.fees,
    0
  );
  subtotal = thankyouDetails?.coachId?.hasGst ? subtotal / 1.18 : subtotal;
  const platformFee = thankyouDetails?.academyId?.platformFee
    ? thankyouDetails.academyId.platformFee / 100
    : 0.12;

  const platformTax = subtotal * platformFee;

  const taxGST = platformTax * 0.18;

  const isSameState = playerHomeState === registeredState;
  const cgst = isSameState ? taxGST / 2 : 0;
  const sgst = isSameState ? taxGST / 2 : 0;
  const igst = taxGST;

  let from =
    "Umn Khel Shiksha Private Limited,Vasant Vihar,Basant Lok Complex,Road 21,New Delhi-110057";
  let billto = "Dummy title,Dummy address ,Dummy address,Dummy-XXXXX";
  let bookingId = thankyouDetails?.bookingId;
  let PlayerGst = thankyouDetails?.playerGST || "N/A";
  let date = new Date(thankyouDetails?.createdAt).toLocaleDateString("en-IN", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });
  let total_amount = thankyouDetails?.pricePaid;
  let gstid = "07AADCU2822L1Z8";

  return (
    <div style={{ padding: "10px" }}>
      <div id="invoice-content" style={{
        maxWidth: "98vw",
        margin: "0 auto",
        backgroundColor: "#fff",
        border: "1px solid #e2e8f0",
        borderRadius: "6px",
        overflow: "hidden",
        fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
      }}>
        {/* Header */}
        <div style={{
          background: "#f7fafc",
          color: "#2d3748",
          padding: "24px 20px",
          textAlign: "center",
          borderBottom: "1px solid #e2e8f0"
        }}>
          <h1 style={{
            fontSize: "2.2rem",
            fontWeight: "700",
            margin: "0"
          }}>
            INVOICE
          </h1>
          <p style={{
            fontSize: "1rem",
            margin: "10px 0 0 0",
            opacity: "0.8"
          }}>
            Professional Sports Training
          </p>
        </div>

        {/* Company and Invoice Details */}
        <div style={{
          padding: "20px",
          display: "flex",
          flexWrap: "wrap",
          gap: "30px",
          borderBottom: "1px solid #e2e8f0"
        }}>
          {/* Company Info */}
          <div className="company-info" style={{ flex: 1, minWidth: "220px" }}>
            <h3 style={{
              color: "#2d3748",
              fontSize: "1.1rem",
              fontWeight: "600",
              margin: "0 0 10px 0"
            }}>
              From:
            </h3>
            <div style={{
              color: "#4a5568",
              lineHeight: "1.5"
            }}>
              {from.split(",").map((item, index) => (
                <p key={index} style={{ margin: "4px 0" }}>{item.trim()}</p>
              ))}
            </div>
            <p style={{
              margin: "12px 0 0 0",
              color: "#2d3748",
              fontWeight: "500",
              fontSize: "0.95rem"
            }}>
              GST ID: <span style={{ color: "#4a5568" }}>{gstid}</span>
            </p>
          </div>



          {/* Invoice Details */}
          <div className="invoice-details" style={{ flex: 1, minWidth: "220px", textAlign: "left" }}>
            <div className="invoice-details-box" style={{
              backgroundColor: "#f7fafc",
              padding: "16px",
              borderRadius: "6px",
              border: "1px solid #e2e8f0"
            }}>
              <div className="invoice-details-row" style={{ marginBottom: "10px" }}>
                <span style={{
                  color: "#2d3748",
                  fontWeight: "600",
                  display: "inline-block",
                  width: "100px",
                  fontSize: "0.98rem"
                }}>
                  Booking ID:
                </span>
                <span style={{
                  color: "#4a5568",
                  fontWeight: "500",
                  fontSize: "0.98rem"
                }}>
                  {bookingId}
                </span>
              </div>
              <div className="invoice-details-row" style={{ marginBottom: "10px" }}>
                <span style={{
                  color: "#2d3748",
                  fontWeight: "600",
                  display: "inline-block",
                  width: "100px",
                  fontSize: "0.98rem"
                }}>
                  Invoice No:
                </span>
                <span style={{
                  color: "#4a5568",
                  fontWeight: "500",
                  fontSize: "0.98rem"
                }}>
                  {thankyouDetails?.invoice}
                </span>
              </div>



              <div className="invoice-details-row" style={{ marginBottom: "10px" }}>
                <span style={{
                  color: "#2d3748",
                  fontWeight: "600",
                  display: "inline-block",
                  width: "100px",
                  fontSize: "0.98rem"
                }}>
                  SAC:
                </span>
                <span style={{
                  color: "#4a5568",
                  fontWeight: "500",
                  fontSize: "0.98rem"
                }}>
                  999799
                </span>
              </div>

              <div className="invoice-details-row" style={{ marginBottom: "10px" }}>
                <span style={{
                  color: "#2d3748",
                  fontWeight: "600",
                  display: "inline-block",
                  width: "100px",
                  fontSize: "0.98rem"
                }}>
                  Date
                </span>
                <span style={{
                  color: "#4a5568",
                  fontWeight: "500",
                  fontSize: "0.98rem"
                }}>
                  {date}
                </span>
              </div>

            </div>
          </div>
        </div>

        {/* Bill To Section */}
        <div className="bill-to" style={{
          padding: "20px",
          borderBottom: "1px solid #e2e8f0"
        }}>
          <h3 style={{
            color: "#2d3748",
            fontSize: "1.1rem",
            fontWeight: "600",
            margin: "0 0 10px 0"
          }}>
            Bill To:
          </h3>
          <div className="bill-to-box" style={{
            backgroundColor: "#f7fafc",
            padding: "12px",
            borderRadius: "6px",
            border: "1px solid #e2e8f0"
          }}>
            <p style={{
              color: "#2d3748",
              fontWeight: "600",
              margin: "0 0 6px 0",
              fontSize: "1rem"
            }}>
              {thankyouDetails?.playerName}
            </p>
            <p style={{
              color: "#4a5568",
              margin: "4px 0",
              fontSize: "0.98rem"
            }}>
              {thankyouDetails?.player?.mobile}
            </p>
            <p style={{
              color: "#4a5568",
              margin: "4px 0",
              fontSize: "0.98rem"
            }}>
              {thankyouDetails?.playerEmail}
            </p>
            <p style={{
              color: "#4a5568",
              margin: "4px 0",
              fontSize: "0.98rem"
            }}>
              Player GST: {thankyouDetails?.playerGST || "N/A"}
            </p>
          </div>
        </div>

        {/* Invoice Items */}
        <div className="invoice-items" style={{ padding: "20px" }}>
          <div className="items-header" style={{
            backgroundColor: "#f7fafc",
            color: "#2d3748",
            padding: "12px 16px",
            borderRadius: "6px 6px 0 0",
            fontWeight: "600",
            fontSize: "1rem",
            border: "1px solid #e2e8f0",
            borderBottom: "none"
          }}>
            Service Details
          </div>
          <div className="items-table" style={{
            border: "1px solid #e2e8f0",
            borderRadius: "0 0 6px 6px",
            overflow: "hidden"
          }}>
            <table style={{
              width: "100%",
              borderCollapse: "collapse"
            }}>
              <thead>
                <tr style={{
                  backgroundColor: "#f7fafc",
                  borderBottom: "1px solid #e2e8f0"
                }}>
                  <th style={{
                    padding: "12px 16px",
                    textAlign: "left",
                    color: "#2d3748",
                    fontWeight: "600",
                    fontSize: "0.98rem"
                  }}>
                    DESCRIPTION
                  </th>
                  <th style={{
                    padding: "12px 16px",
                    textAlign: "right",
                    color: "#2d3748",
                    fontWeight: "600",
                    fontSize: "0.98rem"
                  }}>
                    AMOUNT
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr style={{ borderBottom: "1px solid #e2e8f0" }}>
                  <td style={{
                    padding: "12px 16px",
                    color: "#4a5568",
                    fontSize: "0.98rem"
                  }}>
                    Service Fee
                  </td>
                  <td style={{
                    padding: "12px 16px",
                    textAlign: "right",
                    color: "#4a5568",
                    fontWeight: "500",
                    fontSize: "0.98rem"
                  }}>
                    ₹{platformTax.toFixed(2)}
                  </td>
                </tr>
                {isSameState && (
                  <>
                    <tr style={{ borderBottom: "1px solid #e2e8f0" }}>
                      <td style={{
                        padding: "12px 16px",
                        color: "#4a5568",
                        fontSize: "0.98rem"
                      }}>
                        CGST (9%)
                      </td>
                      <td style={{
                        padding: "12px 16px",
                        textAlign: "right",
                        color: "#4a5568",
                        fontWeight: "500",
                        fontSize: "0.98rem"
                      }}>
                        ₹{cgst?.toFixed(2)}
                      </td>
                    </tr>
                    <tr style={{ borderBottom: "1px solid #e2e8f0" }}>
                      <td style={{
                        padding: "12px 16px",
                        color: "#4a5568",
                        fontSize: "0.98rem"
                      }}>
                        SGST (9%)
                      </td>
                      <td style={{
                        padding: "12px 16px",
                        textAlign: "right",
                        color: "#4a5568",
                        fontWeight: "500",
                        fontSize: "0.98rem"
                      }}>
                        ₹{sgst?.toFixed(2)}
                      </td>
                    </tr>
                  </>
                )}
                {!isSameState && (
                  <tr style={{ borderBottom: "1px solid #e2e8f0" }}>
                    <td style={{
                      padding: "12px 16px",
                      color: "#4a5568",
                      fontSize: "0.98rem"
                    }}>
                      IGST (18%)
                    </td>
                    <td style={{
                      padding: "12px 16px",
                      textAlign: "right",
                      color: "#4a5568",
                      fontWeight: "500",
                      fontSize: "0.98rem"
                    }}>
                      ₹{igst?.toFixed(2)}
                    </td>
                  </tr>
                )}
                <tr style={{
                  backgroundColor: "#f7fafc",
                  color: "#2d3748"
                }}>
                  <td style={{
                    padding: "16px",
                    fontWeight: "700",
                    fontSize: "1.05rem"
                  }}>
                    TOTAL AMOUNT
                  </td>
                  <td style={{
                    padding: "16px",
                    textAlign: "right",
                    fontWeight: "700",
                    fontSize: "1.05rem"
                  }}>
                    ₹{Math.ceil(platformTax + platformTax * 0.18).toFixed(2)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

      </div>

      {/* Print Button */}
      <div style={{
        textAlign: "center",
        marginTop: "18px",
        padding: "10px"
      }}>
        <button
          onClick={handlePrint}
          style={{
            backgroundColor: "#2d3748",
            color: "white",
            border: "none",
            padding: "10px 22px",
            borderRadius: "6px",
            fontSize: "1rem",
            fontWeight: "600",
            cursor: "pointer"
          }}
        >
          🖨️ Print Invoice
        </button>
      </div>
    </div>
  );
};

export default Invoice;
