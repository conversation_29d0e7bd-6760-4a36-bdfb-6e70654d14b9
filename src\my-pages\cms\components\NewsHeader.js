import React from "react";
import {
  Flex,
  Button,
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
} from "@chakra-ui/react";
import { Link } from "react-router-dom";

const NewsHeader = ({
  adjustBtnEdit,
  userData,
  setAdjustBtnEdit,
  setPreviousNewsData,
  newsUpdates,
  onOpen,
  setNewsUpdates,
  previousNewsData,
  posBtnLoading,
  updateNewsPosition
}) => {
  return (
    <Flex justifyContent="space-between" alignItems="center" mb={6}>
      <Breadcrumb fontWeight="medium" fontSize="sm">
        <BreadcrumbItem>
          <BreadcrumbLink as={Link} to="/">Dashboard</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem>
          <BreadcrumbLink as={Link} to="/cms">CMS</BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbItem isCurrentPage>
          <BreadcrumbLink href="#">News & Updates</BreadcrumbLink>
        </BreadcrumbItem>
      </Breadcrumb>
      
      {!adjustBtnEdit ? (
        userData?.accessScopes?.cms?.includes("write") && (
          <Box>
            <Button
              variant="outline"
              colorScheme="telegram"
              size="sm"
              py={5}
              px={4}
              mr={3}
              onClick={() => {
                setAdjustBtnEdit(true);
                setPreviousNewsData(newsUpdates);
              }}
            >
              Adjust Position
            </Button>
            <Button
              variant="outline"
              colorScheme="teal"
              size="sm"
              py={5}
              px={4}
              onClick={onOpen}
            >
              Add News & Update
            </Button>
          </Box>
        )
      ) : (
        <Flex>
          <Button
            variant="outline"
            colorScheme="red"
            size="sm"
            py={5}
            px={4}
            mr={4}
            onClick={() => {
              setNewsUpdates(previousNewsData);
              setAdjustBtnEdit(false);
            }}
          >
            Discard
          </Button>
          <Button
            variant="outline"
            colorScheme="green"
            size="sm"
            py={5}
            px={4}
            isLoading={posBtnLoading}
            onClick={updateNewsPosition}
          >
            Save Changes
          </Button>
        </Flex>
      )}
    </Flex>
  );
};

export default NewsHeader;