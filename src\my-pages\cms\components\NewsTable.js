import React from "react";
import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Flex,
  Text,
  Image,
  Spinner,
  Tooltip,
  Link as ChakraLink,
} from "@chakra-ui/react";
import { MdEdit, MdDelete, MdDragHandle } from "react-icons/md";
import { FiExternalLink } from "react-icons/fi";

const NewsTable = ({
  loading,
  newsUpdates,
  adjustBtnEdit,
  handleDragStart,
  handleDragOver,
  handleDragEnd,
  userData,
  handleEdit,
  openDeleteDialog
}) => {
  return (
    <TableContainer
      mt={6}
      height={`${window.innerHeight - 222}px`}
      overflowY="scroll"
    >
      <Table variant="simple">
        <Thead bgColor="#c1eaee" position="sticky" top="0px" zIndex="99">
          <Tr bgColor="#E2DFDF">
            <Th>Position</Th>
            <Th>Image</Th>
            <Th>Title</Th>
            <Th>Date</Th>
            <Th>Link</Th>
            <Th textAlign="center">Action</Th>
          </Tr>
        </Thead>
        <Tbody>
          {loading ? (
            <Tr>
              <Td></Td>
              <Td></Td>
              <Td display="flex" justifyContent="center" alignItems="center">
                <Spinner />
              </Td>
              <Td></Td>
              <Td></Td>
              <Td></Td>
            </Tr>
          ) : (
            newsUpdates.map((item, index) => (
              <Tr
                key={item._id}
                draggable={adjustBtnEdit}
                onDragStart={() => handleDragStart(index)}
                onDragOver={() => handleDragOver(index)}
                onDragEnd={handleDragEnd}
              >
                <Td fontSize="14px">{index + 1}.</Td>
                <Td>
                  <Image
                    src={item.image}
                    alt={item.title}
                    boxSize="50px"
                    objectFit="cover"
                    borderRadius="md"
                  />
                </Td>
                <Td
                  style={{ whiteSpace: "pre-wrap", wordWrap: "break-word" }}
                  fontSize="14px"
                >
                  {item.title?.substring(0, 30) + (item.title?.length > 30 ? "..." : "")}
                </Td>
                <Td fontSize="14px">
                  {new Date(item.date).toLocaleDateString()}
                </Td>
                <Td fontSize="14px">
                  <ChakraLink
                    href={item.link}
                    isExternal
                    color="blue.500"
                    display="flex"
                    alignItems="center"
                    gap={1}
                  >
                    View <FiExternalLink size={12} />
                  </ChakraLink>
                </Td>
                <Td fontSize="14px">
                  {!adjustBtnEdit ? (
                    <Flex justifyContent="space-evenly" alignItems="center">
                      {userData?.accessScopes?.cms?.includes("write") && (
                        <Tooltip label="Edit News & Update">
                          <Text
                            as="span"
                            cursor="pointer"
                            fontSize="20px"
                            onClick={() => handleEdit(item)}
                          >
                            <MdEdit />
                          </Text>
                        </Tooltip>
                      )}
                      {userData?.accessScopes?.cms?.includes("delete") && (
                        <Tooltip label="Delete News & Update">
                          <Text
                            as="span"
                            cursor="pointer"
                            fontSize="20px"
                            onClick={() => openDeleteDialog(item._id)}
                          >
                            <MdDelete />
                          </Text>
                        </Tooltip>
                      )}
                    </Flex>
                  ) : (
                    <Flex justifyContent="center" alignItems="center">
                      <Text as="span" ml={3}>
                        <MdDragHandle
                          style={{ cursor: "grab" }}
                          fontSize="24px"
                        />
                      </Text>
                    </Flex>
                  )}
                </Td>
              </Tr>
            ))
          )}
          {!loading && newsUpdates.length === 0 && (
            <Tr>
              <Td colSpan={6} textAlign="center" py={8}>
                <Text color="gray.500">No news updates found</Text>
              </Td>
            </Tr>
          )}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

export default NewsTable;