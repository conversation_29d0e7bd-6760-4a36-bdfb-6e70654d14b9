import React, { useState, useEffect } from "react";
import {
  <PERSON>dal,
  ModalOverlay,
  ModalContent,
  Modal<PERSON>eader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Button,
  Image,
  Box,
} from "@chakra-ui/react";

const NewsUpdateModal = ({
  isOpen,
  onClose,
  formData,
  setFormData,
  editingId,
  imageFile,
  setImageFile,
  onSubmit,
  submitting,
}) => {
  const [imagePreview, setImagePreview] = useState(null);

  useEffect(() => {
    if (imageFile) {
      const previewUrl = URL.createObjectURL(imageFile);
      setImagePreview(previewUrl);

      return () => URL.revokeObjectURL(previewUrl);
    } else {
      setImagePreview(null);
    }
  }, [imageFile]);

  const handleCloseModal = () => {
    setFormData({
      title: "",
      image: "",
      date: "",
      link: "",
      position: ""
    });
    setImageFile(null);
    setImagePreview(null);
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleCloseModal} size="lg">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          {editingId ? "Edit News Update" : "Add News Update"}
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <FormControl mb={4} isRequired>
            <FormLabel>Title</FormLabel>
            <Input
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              placeholder="Enter title"
            />
          </FormControl>

          <FormControl mb={4} isRequired>
            <FormLabel>Image</FormLabel>
            <Input
              type="file"
              accept="image/*"
              onChange={(e) => setImageFile(e.target.files[0])}
            />

            {/* Image Preview Section */}
            <Box mt={3}>
              {/* Show preview of newly selected file */}
              {imagePreview && (
                <Box>
                  <FormLabel fontSize="sm" color="gray.600">New Image Preview:</FormLabel>
                  <Image
                    src={imagePreview}
                    alt="New image preview"
                    boxSize="120px"
                    objectFit="cover"
                    borderRadius="md"
                    border="2px solid"
                    borderColor="blue.200"
                  />
                </Box>
              )}

              {formData.image && !imageFile && (
                <Box>
                  <FormLabel fontSize="sm" color="gray.600">Current Image:</FormLabel>
                  <Image
                    src={formData.image}
                    alt="Current image"
                    boxSize="120px"
                    objectFit="cover"
                    borderRadius="md"
                    border="2px solid"
                    borderColor="gray.200"
                  />
                </Box>
              )}
            </Box>
          </FormControl>

          <FormControl mb={4} isRequired>
            <FormLabel>Date</FormLabel>
            <Input
              type="date"
              value={formData.date}
              onChange={(e) => setFormData({ ...formData, date: e.target.value })}
            />
          </FormControl>

          <FormControl mb={4} isRequired>
            <FormLabel>Link</FormLabel>
            <Input
              value={formData.link}
              onChange={(e) => setFormData({ ...formData, link: e.target.value })}
              placeholder="Enter link URL"
            />
          </FormControl>

          <FormControl mb={4}>
            <FormLabel>Position</FormLabel>
            <Input
              type="number"
              value={formData.position}
              onChange={(e) => setFormData({ ...formData, position: e.target.value })}
              placeholder="Enter position (optional)"
            />
          </FormControl>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={handleCloseModal}>
            Cancel
          </Button>
          <Button
            colorScheme="blue"
            onClick={onSubmit}
            isLoading={submitting}
            isDisabled={!formData.title || !formData.date || !formData.link || (!formData.image && !imageFile)}
          >
            {editingId ? "Update" : "Create"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default NewsUpdateModal;