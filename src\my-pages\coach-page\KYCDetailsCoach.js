import {
  Box,
  Button,
  Card,
  CardBody,
  FormControl,
  FormLabel,
  Heading,
  Image,
  Input,
  useToast,
  Flex,
  Select,
  FormErrorMessage,
  RadioGroup,
  Radio,
  HStack,
  Text,
} from "@chakra-ui/react";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";
import { Link, useParams } from "react-router-dom";
import { getCookie } from "../../utilities/auth";
import { useSelector } from "react-redux";
import { Country, State, City } from "country-state-city";

// const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;

const KYCDetailsCoach = ({ coachData }) => {
  const [selectedImages, setSelectedImages] = useState([]);
  const [selectedImagesError, setSelectedImagesError] = useState(false);
  const [isLoadingSubmitBtn, setIsLoadingSubmitBtn] = useState(false);
  const toast = useToast();
  const { id } = useParams();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);
  const [states, setStates] = useState([]);
  const countryCode = "IN";
  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  const handleFileChange = async (e, index) => {
    try {
      const files = Array.from(e.currentTarget.files);
      setSelectedImagesError(false);

      for (const file of files) {
        if (file && file.size > 10 * 1024 * 1024) {
          toast({
            title: "Please select a file less than 10 MB.",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
          continue;
        }

        const formData = new FormData();
        formData.append("image", file);

        const response = await axios.post(
          `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        const url = response?.data?.url;
        if (url) {
          toast({
            title: "Image Uploaded Successfully",
            status: "success",
            duration: 4000,
            position: "top",
            isClosable: true,
          });

          const newImage = { url: url };
          const updatedImages = [...selectedImages, newImage];

          setSelectedImages(updatedImages);
          formik.setFieldValue(`kycDocuments.documentImg`, updatedImages);
        } else {
          toast({
            title:
              "Something went wrong while uploading image, please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const deleteImageFiles = async (url, index) => {
    if (!userData?.accessScopes?.coach?.includes("delete")) {
      toast({
        title: "You don't have an access to perform this action",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else {
      try {
        const formData = new FormData();
        formData.append("url", url);

        const response = await axios.post(
          `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        const resp = response?.data;
        formik.setFieldValue(`kycDocuments.documentImg.${index}.url`, "");
        toast({
          title: "Image removed Successfully",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } catch (error) {
        console.log(error);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      }
    }
  };

  const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;

  // Add helper function to check if coach is editable
  const isKhelcoachCoach = () => {
    const academyId = coachData?.academyId;
    const hasAcademyId = academyId && academyId !== "" && academyId !== null && academyId !== undefined;
    return !hasAcademyId;
  };

  // Update pointer events function
  const getPointerEvents = () => {
    if (!isKhelcoachCoach()) {
      return "none";
    }
    if (!userData?.accessScopes?.coach?.includes("write")) {
      return "none";
    }
    return "auto";
  };

  // Add separate state for Aadhaar images
  const [selectedAadhaarImages, setSelectedAadhaarImages] = useState([]);
  const [selectedAadhaarImagesError, setSelectedAadhaarImagesError] = useState(false);

  // Create separate handler for Aadhaar file changes
  const handleAadhaarFileChange = async (e, index) => {
    try {
      const files = Array.from(e.currentTarget.files);
      setSelectedAadhaarImagesError(false);

      for (const file of files) {
        if (file && file.size > 10 * 1024 * 1024) {
          toast({
            title: "Please select a file less than 10 MB.",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
          continue;
        }

        const formData = new FormData();
        formData.append("image", file);

        const response = await axios.post(
          `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        const url = response?.data?.url;
        if (url) {
          toast({
            title: "Image Uploaded Successfully",
            status: "success",
            duration: 4000,
            position: "top",
            isClosable: true,
          });

          // Store as string URL (like academy format)
          const updatedImages = [...selectedAadhaarImages, url];
          setSelectedAadhaarImages(updatedImages);
          formik.setFieldValue(`aadhaarImage`, updatedImages);
        }
      }
    } catch (error) {
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const formik = useFormik({
    initialValues: {
      kycDocuments: {
        documentName: coachData?.kycDocuments?.documentName || "",
        documentNumber: coachData?.kycDocuments?.documentNumber || "",
        documentImg: Array.isArray(coachData?.kycDocuments?.documentImg)
          ? coachData?.kycDocuments?.documentImg
          : (coachData?.kycDocuments?.documentImg ? [coachData?.kycDocuments?.documentImg] : []),
      },
      aadhaarNumber: coachData?.aadhaarNumber || "",
      aadhaarImage: (() => {
        const aadhaarData = coachData?.aadhaarImage;
        if (Array.isArray(aadhaarData)) {
          // Handle both string URLs and object formats
          return aadhaarData.map(item =>
            typeof item === 'string' ? item : item?.url || ''
          ).filter(url => url);
        }
        return aadhaarData ? [typeof aadhaarData === 'string' ? aadhaarData : aadhaarData?.url || ''] : [];
      })(),
      bankDetails: coachData?.bankDetails || {
        accountNumber: "",
        accountHolderName: "",
        ifsc: "",
      },
      hasGst: coachData?.hasGst || false,
      gstNumber: coachData?.gstNumber || "",
      gstState: coachData?.gstState || "",
    },
    validationSchema: Yup.object().shape({
      kycDocuments: Yup.object().shape({
        documentName: Yup.string().required("Document Name is required"),
        documentNumber: Yup.string()
          .matches(
            /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
            "PAN number must be in format **********"
          )
          .required("Document Number is required"),
        documentImg: Yup.array()
          .min(1, "At least one PAN image is required")
          .of(
            Yup.object().shape({
              url: Yup.string().url("Invalid URL"),
            })
          ),
      }),
      bankDetails: Yup.object().shape({
        accountNumber: Yup.string()
          .matches(/^\d{9,18}$/, "Account number must be 9-18 digits only")
          .min(9, "Account number must be at least 9 digits")
          .max(18, "Account number cannot exceed 18 digits")
          .required("Account number is required"),
        accountHolderName: Yup.string()
          .matches(/^[A-Za-z\s]+$/, "Account holder name must contain only letters and spaces")
          .min(2, "Account holder name must be at least 2 characters")
          .max(100, "Account holder name cannot exceed 100 characters")
          .required("Account holder name is required"),
        ifsc: Yup.string()
          .matches(
            /^[A-Z]{4}0[A-Z0-9]{6}$/,
            "IFSC code must be in format ABCD0123456"
          )
          .length(11, "IFSC code must be exactly 11 characters")
          .required("IFSC Code is required"),
      }),
      hasGst: Yup.boolean(),
      // gstNumber: Yup.string().matches(
      //   gstRegex,
      //   "Please enter a valid GST number"
      // ),
      gstNumber: Yup.string()
        .matches(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, "GST number must be in format 09AABCU9355J1ZS")
        .when("hasGst", {
          is: true,
          then: (schema) => schema.required("GST Number is required"),
          otherwise: (schema) => schema.notRequired(),
        }),
    gstState: Yup.string()
      .when("hasGst", {
        is: true,
        then: (schema) => schema.required("GST State is required"),
        otherwise: (schema) => schema.notRequired(),
      })
      .max(100, "GST State must be less than or equal to 100 characters"),
      aadhaarNumber: Yup.string()
        .matches(/^\d{12}$/, "Must be a valid 12-digit Aadhar number")
        .required("Aadhaar number must be exactly 12 digits (************)"),
      aadhaarImage: Yup.array()
        .min(2, "Please upload at least 2 Aadhaar images")
        .max(2, "Maximum 2 Aadhaar images are allowed")
        .of(Yup.string().url("Invalid URL")),
    }),
    onSubmit: async (values) => {
      // Filter out empty document images
      const filteredDocumentImg = values.kycDocuments.documentImg.filter(
        (doc) => doc.url !== ""
      );
      values.kycDocuments.documentImg = filteredDocumentImg;

      // Filter out empty Aadhaar images
      const filteredAadhaarImg = values.aadhaarImage.filter(
        (img) => img && img.trim() !== ""
      );
      values.aadhaarImage = filteredAadhaarImg;

      setIsLoadingSubmitBtn(true);

      // Basic validation - at least one PAN image required
      if (filteredDocumentImg.length === 0) {
        setSelectedImagesError(true);
        setIsLoadingSubmitBtn(false);
        toast({
          title: "Please upload at least one PAN card image",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        return;
      }

      // Basic validation - PAN images should not exceed 2
      if (filteredDocumentImg.length > 2) {
        setSelectedImagesError(true);
        setIsLoadingSubmitBtn(false);
        toast({
          title: "Maximum 2 PAN card images allowed",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        return;
      }

      // Basic validation - at least 2 Aadhaar images required
      if (filteredAadhaarImg.length < 2) {
        setSelectedAadhaarImagesError(true);
        setIsLoadingSubmitBtn(false);
        toast({
          title: "Please upload at least 2 Aadhaar images",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        return;
      }

      // Basic validation - Aadhaar images should not exceed 2
      if (filteredAadhaarImg.length > 2) {
        setSelectedAadhaarImagesError(true);
        setIsLoadingSubmitBtn(false);
        toast({
          title: "Maximum 2 Aadhaar images allowed",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        return;
      }

      let config = {
        method: "patch",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/coach/${id}`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: JSON.stringify(values),
      };

      axios
        .request(config)
        .then((response) => {
          setIsLoadingSubmitBtn(false);
          toast({
            title: "Coach KYC Details updated",
            status: "success",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log(error);
          setIsLoadingSubmitBtn(false);
          if (error.response?.status === 403) {
            toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    },
  });
  useEffect(() => {
    if (coachData) {


      const panImages = coachData?.kycDocuments?.documentImg || [];

      // Process aadhaar images to ensure they're strings (like academy format)
      const aadhaarData = coachData?.aadhaarImage || [];
      const aadhaarImages = Array.isArray(aadhaarData)
        ? aadhaarData.map(item =>
            typeof item === 'string' ? item : item?.url || ''
          ).filter(url => url)
        : [];

      setSelectedImages(panImages);
      setSelectedAadhaarImages(aadhaarImages);

      // Update formik values as well
      formik.setFieldValue('kycDocuments.documentImg', panImages);
      formik.setFieldValue('aadhaarImage', aadhaarImages);

    }
  }, [coachData]);

  return (
    <>
      <form>
        {/* Document Details */}
        <Card
          pointerEvents={getPointerEvents()}
        >
          <CardBody>
            {!isKhelcoachCoach() && (
              <Box mb={4} p={4} bg="yellow.50" borderRadius="md" border="1px solid" borderColor="yellow.200">
                <Text color="yellow.800" fontWeight="medium" textAlign="center">
                  This Coach details can only be edited by its affiliated academy.
                </Text>
              </Box>
            )}
            <Heading as="h4" size="md" mb={0} flexBasis={"30%"}>
              Document Details
            </Heading>
            <FormControl
              mt={6}
              isInvalid={
                formik.touched.kycDocuments?.documentNumber &&
                formik.errors.kycDocuments?.documentNumber
              }
            >
              <FormLabel htmlFor="kycDocuments.documentNumber">
                PAN Card Number
              </FormLabel>
              <Input
                type="text"
                placeholder="Enter PAN card number"
                name="kycDocuments.documentNumber"
                id="kycDocuments.documentNumber"
                autoComplete="none"
                isReadOnly={!isKhelcoachCoach() || !userData?.accessScopes?.coach?.includes("write")}
                bg={!isKhelcoachCoach() ? "gray.100" : "white"}
                {...formik.getFieldProps("kycDocuments.documentNumber")}
              />
              {formik.touched.kycDocuments?.documentNumber &&
                formik.errors.kycDocuments?.documentNumber && (
                  <FormErrorMessage>
                    {formik.errors.kycDocuments?.documentNumber}
                  </FormErrorMessage>
                )}
            </FormControl>
            <Box mt={3}>
              <Input
                id={`kycDocuments.documentImg.${0}.url`}
                name={`kycDocuments.documentImg.${0}.url`}
                type="file"
                accept="image/*"
                multiple
                key={selectedImages.length > 0 ? 'pan-with-images' : 'pan-no-images'}
                onChange={(e) => handleFileChange(e, 0)}
              />
              {selectedImagesError && selectedImages.length === 0 && (
                <Text color={"red.500"} fontSize={"sm"} mt={1}>
                  Please select atleast one document image
                </Text>
              )}
              {formik?.values?.kycDocuments?.documentImg?.length > 0 && (
                <Box display="flex" flexWrap="wrap" mt={3}>
                  {selectedImages.map(
                    (preview, index) =>
                      preview?.url && (
                        <Box key={index} m={1} textAlign={"center"}>
                          <a
                            href={`${preview?.url}`}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <Image
                              src={preview?.url}
                              alt={`Preview ${index}`}
                              height="11vh"
                              width="7vw"
                            />
                          </a>
                          {userData?.accessScopes?.coach?.includes(
                            "delete"
                          ) && (
                            <Button
                              colorScheme="red"
                              size="sm"
                              mt={1}
                              onClick={() => {
                                deleteImageFiles(
                                  formik?.values?.kycDocuments?.documentImg[
                                    index
                                  ]?.url,
                                  index
                                );
                                formik.setFieldValue(
                                  `kycDocuments.documentImg[${index}].url`,
                                  ""
                                );
                                setSelectedImages(
                                  selectedImages.filter(
                                    (image) => image.url !== preview.url
                                  )
                                );
                              }}
                            >
                              Remove
                            </Button>
                          )}
                        </Box>
                      )
                  )}
                </Box>
              )}
            </Box>
            <FormControl
              mt={6}
              isInvalid={
                formik.touched.aadhaarNumber &&
                formik.errors.aadhaarNumber
              }
            >
              <FormLabel htmlFor="aadhaarNumber">
                Aadhar Number
              </FormLabel>
              <Input
                type="text"
                placeholder="Enter Aadhar Card Number"
                name="aadhaarNumber"
                id="aadhaarNumber"
                autoComplete="none"
                isReadOnly={!isKhelcoachCoach() || !userData?.accessScopes?.coach?.includes("write")}
                bg={!isKhelcoachCoach() ? "gray.100" : "white"}
                {...formik.getFieldProps("aadhaarNumber")}
              />
              {formik.touched.aadhaarNumber &&
                formik.errors.aadhaarNumber && (
                  <FormErrorMessage>
                    {formik.errors.aadhaarNumber}
                  </FormErrorMessage>
                )}
            </FormControl>
            <Box mt={3}>
              <Input
                id={`aadhaarImage.${0}.url`}
                name={`aadhaarImage.${0}.url`}
                type="file"
                accept="image/*"
                multiple
                key={selectedAadhaarImages.length > 0 ? `aadhaar-with-${selectedAadhaarImages.length}-images` : 'aadhaar-no-images'}
                onChange={(e) => handleAadhaarFileChange(e, 0)}
              />
              {selectedAadhaarImagesError && selectedAadhaarImages.length < 2 && (
                <Text color={"red.500"} fontSize={"sm"} mt={1}>
                  Please select at least 2 Aadhaar images
                </Text>
              )}
              {formik.errors.aadhaarImage && (
                <Text color={"red.500"} fontSize={"sm"} mt={1}>
                  {formik.errors.aadhaarImage}
                </Text>
              )}
              {selectedAadhaarImages.length > 0 && (
                <Box display="flex" flexWrap="wrap" mt={3}>
                  {selectedAadhaarImages.map(
                    (imageUrl, index) =>
                      imageUrl && (
                        <Box key={index} m={1} textAlign={"center"}>
                          <a
                            href={`${imageUrl}`}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <Image
                              src={imageUrl}
                              alt={`Aadhaar Preview ${index}`}
                              height="11vh"
                              width="7vw"
                            />
                          </a>
                          {userData?.accessScopes?.coach?.includes("delete") && (
                            <Button
                              colorScheme="red"
                              size="sm"
                              mt={1}
                              onClick={() => {
                                const updatedImages = selectedAadhaarImages.filter(
                                  (image) => image !== imageUrl
                                );
                                setSelectedAadhaarImages(updatedImages);
                                formik.setFieldValue(`aadhaarImage`, updatedImages);
                              }}
                            >
                              Remove
                            </Button>
                          )}
                        </Box>
                      )
                  )}
                </Box>
              )}
            </Box>
          </CardBody>
        </Card>
        {/* Account Details */}
        <Card
          mt={4}
          pointerEvents={getPointerEvents()}
        >
          <CardBody>
            <Heading as="h4" size="md" mb={4} flexBasis={"30%"}>
              Account Details
            </Heading>
            <FormControl
              mb={2}
              isInvalid={
                formik.touched.bankDetails?.accountHolderName &&
                formik.errors.bankDetails?.accountHolderName
              }
            >
              <FormLabel htmlFor="bankDetails.accountHolderName">
                Account Holder Name
              </FormLabel>
              <Input
                type="text"
                placeholder="Enter account holder name"
                name="bankDetails.accountHolderName"
                id="bankDetails.accountHolderName"
                autoComplete="none"
                isReadOnly={!isKhelcoachCoach() || !userData?.accessScopes?.coach?.includes("write")}
                bg={!isKhelcoachCoach() ? "gray.100" : "white"}
                {...formik.getFieldProps("bankDetails.accountHolderName")}
              />
              {formik.touched.bankDetails?.accountHolderName &&
                formik.errors.bankDetails?.accountHolderName && (
                  <FormErrorMessage>
                    {formik.errors.bankDetails?.accountHolderName}
                  </FormErrorMessage>
                )}
            </FormControl>
            <FormControl
              mb={2}
              isInvalid={
                formik.touched.bankDetails?.accountNumber &&
                formik.errors.bankDetails?.accountNumber
              }
            >
              <FormLabel>Account No.</FormLabel>
              <Input
                type="text"
                pattern="\d*"
                placeholder="Enter account number"
                name="bankDetails.accountNumber"
                id="bankDetails.accountNumber"
                isReadOnly={!isKhelcoachCoach() || !userData?.accessScopes?.coach?.includes("write")}
                bg={!isKhelcoachCoach() ? "gray.100" : "white"}
                {...formik.getFieldProps("bankDetails.accountNumber")}
                autoComplete="none"
              />
              {formik.touched.bankDetails?.accountNumber &&
                formik.errors.bankDetails?.accountNumber && (
                  <FormErrorMessage>
                    {formik.errors.bankDetails?.accountNumber}
                  </FormErrorMessage>
                )}
            </FormControl>
            <FormControl
              mb={2}
              isInvalid={
                formik.touched.bankDetails?.ifsc &&
                formik.errors.bankDetails?.ifsc
              }
            >
              <FormLabel>IFSC Code</FormLabel>
              <Input
                type="text"
                name="bankDetails.ifsc"
                id="bankDetails.ifsc"
                autoComplete="none"
                placeholder="Enter IFSC code"
                isReadOnly={!isKhelcoachCoach() || !userData?.accessScopes?.coach?.includes("write")}
                bg={!isKhelcoachCoach() ? "gray.100" : "white"}
                {...formik.getFieldProps("bankDetails.ifsc")}
              />
              {formik.touched.bankDetails?.ifsc &&
                formik.errors.bankDetails?.ifsc && (
                  <FormErrorMessage>
                    {formik.errors.bankDetails?.ifsc}
                  </FormErrorMessage>
                )}
            </FormControl>
          </CardBody>
        </Card>

        {/*GST Details*/}

        <Card
          mt={4}
          pointerEvents={getPointerEvents()}
        >
          <CardBody>
            <Heading as="h4" size="md" mb={4} flexBasis={"30%"}>
              GST Details
            </Heading>

            <FormControl
              isInvalid={formik.touched.hasGst && formik.errors.hasGst} // Validation handling
            >
              <FormLabel htmlFor="hasGst">Has GST?</FormLabel>
              <RadioGroup
                id="hasGst"
                name="hasGst"
                onChange={(value) =>
                  formik.setFieldValue("hasGst", value == "yes" ? true : false)
                }
                value={formik.values?.hasGst ? "yes" : "no"}
                isDisabled={!isKhelcoachCoach() || !userData?.accessScopes?.coach?.includes("write")}
              >
                <HStack spacing="24px">
                  <Radio value="yes">Yes</Radio>
                  <Radio value="no">No</Radio>
                </HStack>
              </RadioGroup>
              <FormErrorMessage>{formik.errors.hasGst}</FormErrorMessage>
            </FormControl>

            {formik.values.hasGst && (
              <FormControl
                mb={2}
                isInvalid={formik.touched.gstNumber && formik.errors.gstNumber}
              >
                <FormLabel>GST No.</FormLabel>
                <Input
                  type="text"
                  pattern="\d*"
                  placeholder="Enter GST number"
                  name="gstNumber"
                  id="gstNumber"
                  isReadOnly={!isKhelcoachCoach() || !userData?.accessScopes?.coach?.includes("write")}
                  bg={!isKhelcoachCoach() ? "gray.100" : "white"}
                  {...formik.getFieldProps("gstNumber")}
                  autoComplete="none"
                />
                {formik.touched.gstNumber && formik.errors.gstNumber && (
                  <FormErrorMessage>{formik.errors.gstNumber}</FormErrorMessage>
                )}
              </FormControl>
            )}
            {formik.values.hasGst && (
              <FormControl
                flexBasis={"48%"}
                isInvalid={formik.touched.gstState && formik.errors.gstState}
              >
                <FormLabel htmlFor="gstState">GST State</FormLabel>

                {formik.values.hasGst && (
                  // Display the state name based on gstState (
                  // Render Select input if gstState is not available
                  <Select
                    placeholder="Select GST State"
                    name="gstState"
                    id="gstState"
                    autoComplete="address-level1"
                    isDisabled={!isKhelcoachCoach() || !userData?.accessScopes?.coach?.includes("write")}
                    bg={!isKhelcoachCoach() ? "gray.100" : "white"}
                    {...formik.getFieldProps("gstState")}
                  >
                    <option value="">Select State</option>
                    {states.map((state) => (
                      <option key={state.isoCode} value={state.isoCode}>
                        {state.name}
                      </option>
                    ))}
                  </Select>
                )}

                <FormErrorMessage>{formik.errors.gstState}</FormErrorMessage>
              </FormControl>
            )}
          </CardBody>
        </Card>

        {userData?.accessScopes?.coach?.includes("write") && isKhelcoachCoach() && (
          <Flex justifyContent={"space-between"} alignItems={"center"} mt={4}>
            <Button
              colorScheme="red"
              flexBasis={"49%"}
              onClick={() => {
                formik.resetForm();
                toast({
                  title: "All Changes has been discarded",
                  status: "success",
                  duration: 4000,
                  position: "top",
                  isClosable: true,
                });
              }}
            >
              Discard
            </Button>
            <Button
              colorScheme="green"
              flexBasis={"49%"}
              type="submit"
              onClick={formik.handleSubmit}
              isLoading={isLoadingSubmitBtn}
            >
              Update
            </Button>
          </Flex>
        )}
        {!isKhelcoachCoach() && (
          <Box
            p={4}
            bg="gray.100"
            borderRadius="md"
            textAlign="center"
            mt={4}
          >
            <Text color="gray.600" fontSize="sm">
              This coach is affiliated with an academy. KYC details cannot be modified.
            </Text>
          </Box>
        )}
      </form>
    </>
  );
};

export default KYCDetailsCoach;
