import { useState } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Card,
  Flex,
  Input,
  Switch,
  Text,
  Image,
  IconButton,
  Tag,
  TagLabel,
  TagCloseButton,
  Wrap,
  useToast,
  Tooltip,
} from "@chakra-ui/react";
import { Link, useNavigate } from "react-router-dom";
import { Quill } from "../../components";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { ArrowBackIcon, CloseIcon } from "@chakra-ui/icons";
import axios from "axios";
import { IoMdArrowRoundBack } from "react-icons/io";

// Yup validation schema
const BlogSchema = Yup.object().shape({
  title: Yup.string().required("Title is required"),
  handle: Yup.string().optional(),
  writerName: Yup.string().required("Writer name is required"),
  writerDesc: Yup.string().required("Writer description is required"),
  writerDesignation: Yup.string().required("Writer designation is required"),
});

const CreateBlogPost = () => {
  const [visible, setVisible] = useState(true);
  const [coverImage, setCoverImage] = useState(null);
  const [writerImage, setWriterImage] = useState(null);
  const [tags, setTags] = useState([]);
  const [tagInput, setTagInput] = useState("");
  const [content, setContent] = useState("");
  const toast = useToast();
  const navigate = useNavigate();

  const handleFileChange = (e, setImage) => {
    const file = e.target.files[0];
    if (file) {
      setImage({
        file,
        preview: URL.createObjectURL(file),
      });
    }
  };

  const removeImage = (setImage) => {
    setImage(null);
  };

  const addTag = (e) => {
    e.preventDefault();
    if (tagInput.trim() !== "" && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
    }
    setTagInput("");
  };

  const removeTag = (tagToRemove) => {
    setTags(tags.filter((t) => t !== tagToRemove));
  };

  const uploadImage = async (file) => {
    const formData = new FormData();
    formData.append("image", file);

    const response = await axios.post(
      `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );

    return response?.data?.url; // make sure your backend returns the uploaded URL here
  };


  return (
    <Box bg={"#f2f2f2"}>
      <Layout title="CMS | Blocks" content="container">
        <Flex justifyContent={"space-between"} alignItems={"center"} mb={6}>
          <Flex justifyContent={"center"} alignItems={"center"}>
            <Tooltip label="Back">
              <Text
                mr={3}
                as={"span"}
                fontSize={"28px"}
                cursor={"pointer"}
                onClick={() => navigate(-1)}
              >
                <IoMdArrowRoundBack />
              </Text>
            </Tooltip>
            <Breadcrumb fontWeight="medium" fontSize="sm">
              <BreadcrumbItem>
                <BreadcrumbLink>
                  <Link to={"/"}>Dashboard</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink>CMS</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Create Blog Post</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          <Button
            form="blogForm"
            type="submit"
            variant={"outline"}
            colorScheme={"teal"}
            size={"sm"}
            py={5}
            px={4}
            mr={2}
          >
            Create Blog Post
          </Button>
        </Flex>

        {/* Formik Form */}
        <Formik
          initialValues={{
            title: "",
            handle: "",
            writerName: "",
            writerDesc: "",
            writerDesignation: "",
          }}
          validationSchema={BlogSchema}
          onSubmit={async (values, { setSubmitting }) => {
            setSubmitting(true);
            const token = sessionStorage.getItem("admintoken");
            // Validation
            if (!coverImage?.file) {
              toast({
                title: "Cover image is required",
                status: "error",
                duration: 3000,
                isClosable: true,
              });
              setSubmitting(false);
              return;
            }

            if (!content.trim()) {
              toast({
                title: "Content is required",
                status: "error",
                duration: 3000,
                isClosable: true,
              });
              setSubmitting(false);
              return;
            }

            try {
              // Upload cover and writer image if selected
              const uploadedCover = await uploadImage(coverImage.file);
              const uploadedWriter =
                writerImage?.file ? await uploadImage(writerImage.file) : "";

              // Construct payload
              const payload = {
                blogName: values.title,
                description: content,
                handle: values.handle,
                isVisible: visible,
                featureImage: uploadedCover,
                writerName: values.writerName,
                writerShortname: values.writerDesc,
                writerDesignation: values.writerDesignation,
                writerImage: uploadedWriter,
                tag: tags,
                publishDate: new Date().toISOString(),
              };

              // Axios config
              const config = {
                method: "post",
                maxBodyLength: Infinity,
                url: `${process.env.REACT_APP_BASE_URL}/api/blogs`,
                headers: {
                  Authorization: `${token}`,
                  "Content-Type": "application/json",
                },
                data: payload,
              };

              const response = await axios.request(config);

              toast({
                title: "Blog created",
                description: "Your blog post was created successfully",
                status: "success",
                duration: 3000,
                isClosable: true,
              });
              navigate("/cms/blog-post");
            } catch (error) {
              console.error(error);
              if (error.response?.status === 403) {
                toast({
                  title: "You don't have access to perform this action",
                  status: "warning",
                  duration: 4000,
                  isClosable: true,
                });
              } else if (error.response?.status === 401) {
                toast({
                  title: "Session expired, please login",
                  status: "warning",
                  duration: 4000,
                  isClosable: true,
                });
              } else {
                toast({
                  title: "Something went wrong, please try again later",
                  status: "error",
                  duration: 4000,
                  isClosable: true,
                });
              }
            } finally {
              setSubmitting(false);
            }
          }}
        >
          {({ errors, touched, handleChange, values }) => (
            <Form id="blogForm">
              <Flex justifyContent={"space-between"} alignItems={"flex-start"} gap={6} mt={4}>
                {/* Left Card */}
                <Card width={"70%"} p={4} borderRadius={"md"}>
                  <Text fontSize={"sm"} fontWeight={"semibold"} mb={1}>
                    Title <Text as="span" color="red.500">*</Text>
                  </Text>
                  <Input
                    name="title"
                    placeholder="Give your blog post a title"
                    size="sm"
                    mb={2}
                    value={values.title}
                    onChange={handleChange}
                  />
                  {errors.title && touched.title && (
                    <Text color="red.500" fontSize="xs">{errors.title}</Text>
                  )}

                  <Text fontSize={"sm"} fontWeight={"semibold"} mb={1} mt={3}>
                    Content <Text as="span" color="red.500">*</Text>
                  </Text>
                  <Quill value={content} onChange={setContent} />

                  <Text fontSize={"sm"} fontWeight={"semibold"} mb={1} mt={20} py={2}>
                    Handle
                  </Text>
                  <Input
                    name="handle"
                    placeholder="Enter handle"
                    size="sm"
                    mb={2}
                    value={values.handle}
                    onChange={handleChange}
                  />
                  {errors.handle && touched.handle && (
                    <Text color="red.500" fontSize="xs">{errors.handle}</Text>
                  )}
                </Card>

                {/* Right Sidebar */}
                <Box width={"30%"}>
                  {/* Visibility */}
                  <Card p={4} borderRadius={"md"} mb={4}>
                    <Text fontSize={"sm"} fontWeight={"semibold"} mb={1}>
                      Visibility
                    </Text>
                    <Flex
                      align={"center"}
                      justify="space-between"
                      border={"1px"}
                      borderColor={"gray.300"}
                      borderRadius={"md"}
                      p={2}
                    >
                      <Text fontSize={"sm"} fontWeight={"semibold"} mb={0}>
                        {visible ? "Visible" : "Hidden"}
                      </Text>
                      <Switch
                        colorScheme="teal"
                        size="sm"
                        isChecked={visible}
                        onChange={() => setVisible(!visible)}
                      />
                    </Flex>
                  </Card>

                  {/* Blog Cover Image */}
                  <Card p={4} borderRadius={"md"} mb={4}>
                    <Text fontSize={"sm"} fontWeight={"semibold"} mb={2}>
                      Blog Cover Image <Text as="span" color="red.500">*</Text>
                    </Text>
                    {!coverImage ? (
                      <Input
                        type="file"
                        accept="image/*"
                        size="sm"
                        onChange={(e) => handleFileChange(e, setCoverImage)}
                      />
                    ) : (
                      <Box position="relative" width={110}>
                        <Image src={coverImage.preview} alt="Cover Preview" borderRadius="md" />
                        <IconButton
                          aria-label="Remove image"
                          icon={<CloseIcon />}
                          size="xs"
                          colorScheme="red"
                          position="absolute"
                          top={2}
                          right={2}
                          onClick={() => removeImage(setCoverImage)}
                        />
                      </Box>
                    )}
                  </Card>

                  {/* Writer's Details */}
                  <Card p={4} borderRadius={"md"}>
                    <Text fontSize={"sm"} fontWeight={"semibold"} mb={2}>
                      Writer's Details
                    </Text>

                    <Text fontSize={"sm"} fontWeight={"semibold"} mt={3}>
                      Writer Name
                    </Text>
                    <Input
                      name="writerName"
                      placeholder="Enter writer name"
                      size="sm"
                      mb={2}
                      value={values.writerName}
                      onChange={handleChange}
                    />
                    {errors.writerName && touched.writerName && (
                      <Text color="red.500" fontSize="xs">{errors.writerName}</Text>
                    )}

                    <Text fontSize={"sm"} fontWeight={"semibold"} mt={3}>
                      Writer short description
                    </Text>
                    <Input
                      name="writerDesc"
                      placeholder="Enter writer description"
                      size="sm"
                      mb={2}
                      value={values.writerDesc}
                      onChange={handleChange}
                    />
                    {errors.writerDesc && touched.writerDesc && (
                      <Text color="red.500" fontSize="xs">{errors.writerDesc}</Text>
                    )}

                    <Text fontSize={"sm"} fontWeight={"semibold"} mt={3}>
                      Writer designation
                    </Text>
                    <Input
                      name="writerDesignation"
                      placeholder="Enter writer designation"
                      size="sm"
                      mb={2}
                      value={values.writerDesignation}
                      onChange={handleChange}
                    />
                    {errors.writerDesignation && touched.writerDesignation && (
                      <Text color="red.500" fontSize="xs">{errors.writerDesignation}</Text>
                    )}

                    <Text fontSize={"sm"} fontWeight={"semibold"} mt={3}>
                      Writer Image
                    </Text>
                    {!writerImage ? (
                      <Input
                        type="file"
                        accept="image/*"
                        size="sm"
                        onChange={(e) => handleFileChange(e, setWriterImage)}
                      />
                    ) : (
                      <Box position="relative" mt={2} width={110}>
                        <Image src={writerImage.preview} alt="Writer Preview" borderRadius="md" />
                        <IconButton
                          aria-label="Remove image"
                          icon={<CloseIcon />}
                          size="xs"
                          colorScheme="red"
                          position="absolute"
                          top={2}
                          right={2}
                          onClick={() => removeImage(setWriterImage)}
                        />
                      </Box>
                    )}

                    {/* Tags */}
                    <Text fontSize={"sm"} fontWeight={"semibold"} mt={3}>
                      Tags
                    </Text>
                    <Flex gap={2} mt={1}>
                      <Input
                        placeholder="Type a tag and press Enter"
                        size="sm"
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        onKeyDown={(e) => e.key === "Enter" && addTag(e)}
                      />
                      <Button size="sm" onClick={addTag}>
                        Add
                      </Button>
                    </Flex>
                    <Wrap mt={2}>
                      {tags.map((tag) => (
                        <Tag
                          key={tag}
                          borderRadius="full"
                          variant="solid"
                          colorScheme="teal"
                          size="sm"
                        >
                          <TagLabel>{tag}</TagLabel>
                          <TagCloseButton onClick={() => removeTag(tag)} />
                        </Tag>
                      ))}
                    </Wrap>
                  </Card>
                </Box>
              </Flex>
            </Form>
          )}
        </Formik>
      </Layout>
    </Box>
  );
};

export default CreateBlogPost;