import { useState, useEffect } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Card,
  Flex,
  Input,
  Switch,
  Text,
  Image,
  IconButton,
  Tag,
  TagLabel,
  TagCloseButton,
  Wrap,
  useToast,
  Spinner,
  Tooltip,
} from "@chakra-ui/react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { Quill } from "../../components";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { CloseIcon } from "@chakra-ui/icons";
import axios from "axios";
import { IoMdArrowRoundBack } from "react-icons/io";

// ✅ Yup validation schema
const BlogSchema = Yup.object().shape({
  title: Yup.string().required("Title is required"),
  handle: Yup.string().required("Handle is required"),
  writerName: Yup.string().required("Writer name is required"),
  writerDesc: Yup.string().required("Writer description is required"),
  writerDesignation: Yup.string().required("Writer designation is required"),
});

const EditBlogPost = () => {
  let { id } = useParams(); // Get blog ID from route
  const toast = useToast();
  const [loading, setLoading] = useState(true);
  const [visible, setVisible] = useState(true);
  const [coverImage, setCoverImage] = useState(null);
  const [writerImage, setWriterImage] = useState(null);
  const [tags, setTags] = useState([]);
  const [tagInput, setTagInput] = useState("");
  const navigate = useNavigate();

  const token = sessionStorage.getItem("admintoken");

  // Fetch blog details on mount
  useEffect(() => {
    setLoading(true);
    const fetchBlog = async () => {
      try {
        const res = await axios.get(
          `${process.env.REACT_APP_BASE_URL}/api/blogs/${id}`,
          {
            headers: { Authorization: `${token}` },
          }
        );

        const data = res.data.data;
        setVisible(data.isVisible);
        setTags(data.tag || []);
        setCoverImage(data.featureImage ? { preview: data.featureImage } : null);
        setWriterImage(data.writerImage ? { preview: data.writerImage } : null);
        setInitialValues({
          title: data.blogName,
          handle: data.handle,
          writerName: data.writerName,
          writerDesc: data.writerShortname,
          writerDesignation: data.writerDesignation,
          description: data.description,
        });
      } catch (err) {
        console.error(err);
        toast({
          title: "Error",
          description: "Failed to fetch blog details",
          status: "error",
          duration: 4000,
          isClosable: true,
        });
      } finally {
        setLoading(false);
      }
    };
    fetchBlog();
  }, [id, token, toast]);

  const [initialValues, setInitialValues] = useState({
    title: "",
    handle: "",
    writerName: "",
    writerDesc: "",
    writerDesignation: "",
    description: "",
  });

  const handleFileChange = (e, setImage) => {
    const file = e.target.files[0];
    if (file) {
      setImage({ file, preview: URL.createObjectURL(file) });
    }
  };

  const removeImage = (setImage) => setImage(null);

  const addTag = (e) => {
    e.preventDefault();
    if (tagInput.trim() !== "" && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
    }
    setTagInput("");
  };

  const removeTag = (tagToRemove) => setTags(tags.filter((t) => t !== tagToRemove));

  if (loading)
    return (
      <Box bg="#f2f2f2">
        <Layout title="CMS | Edit Blog" content="container">
          <Flex minH="calc(100vh - 190px)" align="center" justify="center">
            <Spinner size="xl" />
          </Flex>
        </Layout>
      </Box>
    );

  return (
    <Box bg="#f2f2f2">
      <Layout title="CMS | Edit Blog" content="container">
        <Formik
          enableReinitialize
          initialValues={initialValues}
          validationSchema={BlogSchema}
          onSubmit={async (values, { setSubmitting }) => {
            setSubmitting(true);

            const payload = {
              blogName: values.title,
              description: values.description,
              isVisible: visible,
              featureImage: coverImage?.preview || "",
              writerName: values.writerName,
              writerShortname: values.writerDesc,
              writerDesignation: values.writerDesignation,
              writerImage: writerImage?.preview || "",
              tag: tags,
              publishDate: new Date().toISOString(),
            };

            try {
              const res = await axios.patch(
                `${process.env.REACT_APP_BASE_URL}/api/blogs/${id}`,
                payload,
                { headers: { Authorization: `${token}` } }
              );

              toast({
                title: "Blog updated",
                description: "Your blog post was updated successfully",
                status: "success",
                duration: 3000,
                isClosable: true,
              });

            } catch (err) {
              console.error(err);
              toast({
                title: "Error",
                description: "Failed to update blog post",
                status: "error",
                duration: 4000,
                isClosable: true,
              });
            } finally {
              setSubmitting(false);
            }
          }}
        >
          {({ errors, touched, handleChange, values, setFieldValue }) => (
            <Form>
              {/* Header with Breadcrumb + Save Changes button */}
              <Flex justifyContent={"space-between"} alignItems={"center"} mb={6}>
                <Flex justifyContent={"center"} alignItems={"center"}>
                  <Tooltip label="Back">
                    <Text
                      mr={3}
                      as={"span"}
                      fontSize={"28px"}
                      cursor={"pointer"}
                      onClick={() => navigate(-1)}
                    >
                      <IoMdArrowRoundBack />
                    </Text>
                  </Tooltip>
                  <Breadcrumb fontWeight="medium" fontSize="sm">
                    <BreadcrumbItem>
                      <BreadcrumbLink>
                        <Link to={"/"}>Dashboard</Link>
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbItem isCurrentPage>
                      <BreadcrumbLink>CMS</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbItem isCurrentPage>
                      <BreadcrumbLink href="#">Edit Blog Post</BreadcrumbLink>
                    </BreadcrumbItem>
                  </Breadcrumb>
                </Flex>
                <Button type="submit" colorScheme="teal">
                  Save Changes
                </Button>
              </Flex>

              <Flex gap={6} mt={4}>
                {/* Left Card */}
                <Card width="70%" p={4} borderRadius="md">
                  <Text fontSize="sm" fontWeight="semibold" mb={1}>
                    Title
                  </Text>
                  <Input
                    name="title"
                    placeholder="Give your blog post a title"
                    size="sm"
                    mb={2}
                    value={values.title}
                    onChange={handleChange}
                  />
                  {errors.title && touched.title && (
                    <Text color="red.500" fontSize="xs">
                      {errors.title}
                    </Text>
                  )}

                  <Text fontSize="sm" fontWeight="semibold" mb={1} mt={3}>
                    Content
                  </Text>
                  <Quill
                    value={values.description}
                    onChange={(val) => setFieldValue('description', val)}
                    height={300}
                  />

                  <Text fontSize={"sm"} fontWeight={"semibold"} mb={1} mt={20} py={2}>
                    Handle
                  </Text>
                  <Input
                    name="handle"
                    placeholder="Enter handle"
                    size="sm"
                    mb={2}
                    w="100%"
                    value={values.handle}
                    onChange={handleChange}
                    isDisabled
                  />
                  {errors.handle && touched.handle && (
                    <Text color="red.500" fontSize="xs">
                      {errors.handle}
                    </Text>
                  )}
                </Card>

                {/* Right Sidebar */}
                <Box width="30%">
                  {/* Visibility */}
                  <Card p={4} borderRadius="md" mb={4}>
                    <Text fontSize="sm" fontWeight="semibold" mb={1}>
                      Visibility
                    </Text>
                    <Flex
                      align="center"
                      justify="space-between"
                      border="1px"
                      borderColor="gray.300"
                      borderRadius="md"
                      p={2}
                    >
                      <Text fontSize="sm" fontWeight="semibold" mb={0}>
                        {visible ? "Visible" : "Hidden"}
                      </Text>
                      <Switch
                        colorScheme="teal"
                        size="sm"
                        isChecked={visible}
                        onChange={() => setVisible(!visible)}
                      />
                    </Flex>
                  </Card>

                  {/* Blog Cover Image */}
                  <Card p={4} borderRadius="md" mb={4}>
                    <Text fontSize="sm" fontWeight="semibold" mb={2}>
                      Blog Cover Image
                    </Text>
                    {!coverImage ? (
                      <Input
                        type="file"
                        accept="image/*"
                        size="sm"
                        onChange={(e) => handleFileChange(e, setCoverImage)}
                      />
                    ) : (
                      <Box position="relative" width={110}>
                        <Image
                          src={coverImage.preview}
                          alt="Cover Preview"
                          borderRadius="md"
                        />
                        <IconButton
                          aria-label="Remove image"
                          icon={<CloseIcon />}
                          size="xs"
                          colorScheme="red"
                          position="absolute"
                          top={2}
                          right={2}
                          onClick={() => removeImage(setCoverImage)}
                        />
                      </Box>
                    )}
                  </Card>

                  {/* Writer's Details */}
                  <Card p={4} borderRadius="md">
                    <Text fontSize="sm" fontWeight="semibold" mb={2}>
                      Writer's Details
                    </Text>

                    <Text fontSize="sm" fontWeight="semibold" mt={3}>
                      Writer Name
                    </Text>
                    <Input
                      name="writerName"
                      placeholder="Enter writer name"
                      size="sm"
                      mb={2}
                      value={values.writerName}
                      onChange={handleChange}
                    />
                    {errors.writerName && touched.writerName && (
                      <Text color="red.500" fontSize="xs">
                        {errors.writerName}
                      </Text>
                    )}

                    <Text fontSize="sm" fontWeight="semibold" mt={3}>
                      Writer short description
                    </Text>
                    <Input
                      name="writerDesc"
                      placeholder="Enter writer description"
                      size="sm"
                      mb={2}
                      value={values.writerDesc}
                      onChange={handleChange}
                    />
                    {errors.writerDesc && touched.writerDesc && (
                      <Text color="red.500" fontSize="xs">
                        {errors.writerDesc}
                      </Text>
                    )}

                    <Text fontSize="sm" fontWeight="semibold" mt={3}>
                      Writer designation
                    </Text>
                    <Input
                      name="writerDesignation"
                      placeholder="Enter writer designation"
                      size="sm"
                      mb={2}
                      value={values.writerDesignation}
                      onChange={handleChange}
                    />
                    {errors.writerDesignation && touched.writerDesignation && (
                      <Text color="red.500" fontSize="xs">
                        {errors.writerDesignation}
                      </Text>
                    )}

                    <Text fontSize="sm" fontWeight="semibold" mt={3}>
                      Writer Image
                    </Text>
                    {!writerImage ? (
                      <Input
                        type="file"
                        accept="image/*"
                        size="sm"
                        onChange={(e) => handleFileChange(e, setWriterImage)}
                      />
                    ) : (
                      <Box position="relative" mt={2} width={110}>
                        <Image
                          src={writerImage.preview}
                          alt="Writer Preview"
                          borderRadius="md"
                        />
                        <IconButton
                          aria-label="Remove image"
                          icon={<CloseIcon />}
                          size="xs"
                          colorScheme="red"
                          position="absolute"
                          top={2}
                          right={2}
                          onClick={() => removeImage(setWriterImage)}
                        />
                      </Box>
                    )}

                    {/* Tags */}
                    <Text fontSize="sm" fontWeight="semibold" mt={3}>
                      Tags
                    </Text>
                    <Flex gap={2} mt={1}>
                      <Input
                        placeholder="Type a tag and press Enter"
                        size="sm"
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        onKeyDown={(e) => e.key === "Enter" && addTag(e)}
                      />
                      <Button size="sm" onClick={addTag}>
                        Add
                      </Button>
                    </Flex>
                    <Wrap mt={2}>
                      {tags.map((tag) => (
                        <Tag
                          key={tag}
                          borderRadius="full"
                          variant="solid"
                          colorScheme="teal"
                          size="sm"
                        >
                          <TagLabel>{tag}</TagLabel>
                          <TagCloseButton onClick={() => removeTag(tag)} />
                        </Tag>
                      ))}
                    </Wrap>
                  </Card>
                </Box>
              </Flex>
            </Form>
          )}
        </Formik>
      </Layout>
    </Box>
  );
};

export default EditBlogPost;
