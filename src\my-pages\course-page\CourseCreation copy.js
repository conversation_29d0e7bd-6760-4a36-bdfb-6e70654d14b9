"use client";
import { useState, Fragment, useEffect } from "react";
import { Listbox, Transition } from "@headlessui/react";
import { CheckIcon, ChevronUpDownIcon } from "@heroicons/react/20/solid";
import { CheckCircleIcon, FaceSmileIcon } from "@heroicons/react/24/outline";
import { XMarkIcon } from "@heroicons/react/20/solid";
import { XCircleIcon } from "@heroicons/react/20/solid";
import dynamic from "next/dynamic";
import "../../../../components/CourseCreation/datePicker.css";
import axios from "axios";
import DatePicker from "react-datepicker";
import { useRouter } from "next/navigation";
import IndepentClasses from "@/components/CourseCreation/IndependentClasses";
import SpecificClasses from "@/components/CourseCreation/SpecificClasses";

const ReactQuill = dynamic(() => import("react-quill"), {
  ssr: false,
});
import "react-quill/dist/quill.snow.css";
// import IndepentClasses from "@/components/CourseCreation/IndependentClasses";
import API from "@/components/API";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const daysOfWeek = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

const proficiencyLevelOption = [
  { id: 0, name: "Beginner", value: "beginner" },
  { id: 1, name: "Intermediate", value: "intermediate" },
  { id: 2, name: "Advance", value: "advance" },
];

const sessionTypeOption = [
  { id: 0, name: "Group", value: "group" },
  { id: 1, name: "Individual", value: "individual" },
];

export default function CreateCourse() {
  const [courseName, setCourseName] = useState("");
  const [courseNameError, setCourseNameError] = useState(false);
  const [isItCamp, setIsItCamp] = useState(false);
  const [campName, setCampName] = useState("");
  const [campNameError, setCampNameError] = useState(false);
  const [maximumGroupSize, setMaximumGroupSize] = useState(1);
  const [maximumGroupSizeError, setMaximumGroupSizeError] = useState(false);
  const [sessionType, setSessionType] = useState(sessionTypeOption[0]);
  const [categoryType, setCategoryType] = useState({});
  const [categories, setCategories] = useState([]);
  const [courseDescription, setCourseDescription] = useState("");
  const [courseDescriptionError, setCourseDescriptionError] = useState(false);
  const [courseAmeneties, setCourseAmeneties] = useState("");
  const [courseAmenetiesError, setCourseAmenetiesError] = useState(false);
  const [show, setShow] = useState(false);
  const [show2, setShow2] = useState(false);
  const [btnIsLoading, setBtnIsLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState([]);
  const [selectedImages, setSelectedImages] = useState([]);
  const [facilities, setFacilities] = useState([]);
  const [selectedDays, setSelectedDays] = useState([]);
  const [isEnd, setIsEnd] = useState(false);
  const [timeFrom, setTimeFrom] = useState("");
  const [timeTo, setTimeTo] = useState("");
  const [classType, setClassType] = useState("class");
  const [startDate, setStartDate] = useState("");
  const [startDateError, setStartDateError] = useState(false);
  const [endDate, setEndDate] = useState("");
  const [price, setPrice] = useState("");
  const [priceError, setPriceError] = useState(false);
  const [fees30, setFees30] = useState("");
  const [fees60, setFees60] = useState("");
  const [fees30Error, setFees30Error] = useState(false);
  const [fees60Error, setFees60Error] = useState(false);
  const [cancellationPolicy, setCancellationPolicy] = useState("");
  const [carryThings, setCarryThings] = useState("");
  const [timeFromError, setTimeFromError] = useState(false);
  const [timeToError, setTimeToError] = useState(false);
  const [courseFacility, setCourseFacility] = useState({});
  const [categoryError, setCategoryError] = useState(false);
  const [facilityError, setFacilityError] = useState(false);
  const [selectedDaysError, setSelectedDaysError] = useState(false);
  const [message, setMessage] = useState(
    "There was an errors with your submission, please try again later"
  );
  const [coach, setCoach] = useState({});

  const [proficiencyLevel, setProficiencyLevel] = useState(
    proficiencyLevelOption[0]
  );

  const router = useRouter();

  const getFacilities = async () => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let requestOptions = {
        method: "GET",
        headers: myHeaders,
      };
      const response = await fetch(`/api/coach_profile`, requestOptions);

      const result = await response.json();



      if (!result.error) {
        setFacilities(result.linkedFacilities);
        setCoach(result);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const getCurrentTime = () => {
    const currentDate = new Date();
    const currentHours = currentDate.getHours();
    const currentMinutes = currentDate.getMinutes();
    return new Date(0, 0, 0, currentHours, currentMinutes);
  };

  const currentTime = getCurrentTime();

  const date = new Date(startDate);
  const today = new Date();

  const includesToday =
    date.getDate() <= today.getDate() &&
    date.getMonth() <= today.getMonth() &&
    date.getFullYear() <= today.getFullYear();

  const filterPassedTime = (time) => {
    if (!timeFrom) return true;
    const selectedTime = new Date(time);
    const minAllowedTime = getMinTime();
    return selectedTime >= minAllowedTime;
  };

  const minTime = includesToday ? currentTime : undefined;

  const getMinTime = () => {
    if (!timeFrom) return null;

    const timeFromObj = new Date(timeFrom);
    const tenMinutesLater = new Date(timeFromObj.getTime() + 10 * 60000); // 10 minutes in milliseconds

    return tenMinutesLater;
  };

  // const getEndTime = () => {
  //   if (!timeFrom) return null;

  //   const timeFromObj = new Date(timeFrom);
  //   const tenMinutesLater = new Date(timeFromObj.getTime() + 10 * 60000); // 10 minutes in milliseconds
  //   setMinEndTime(tenMinutesLater);
  //    console.log(timeFrom,"pppppp");
  // };

  // useEffect(() => {
  //   getEndTime();
  // }, [startDate, timeFrom, setTimeFrom, setStartDate]);

  const getCategories = async () => {
    try {
      let response = await axios.get(`${API}/api/category`);
      // console.log(response.data, "ooooooo");
      setCategories(response.data);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getCategories();
    getFacilities();
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
  };

  const handleFileChange = async (e) => {
    try {
      const file = e.target.files[0];

      if (file && file.size > 10 * 1024 * 1024) {
        setShow2(true);
        setMessage("Please select a file less than 10 MB.");
        setTimeout(() => {
          setShow2(false);
        }, 3000);
        return;
      }

      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${API}/api/course/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = await response?.data?.url;

      setImageUrl([...imageUrl, { url: url }]);
      setSelectedImages([...selectedImages, { url: url }]);
    } catch (error) {
      console.log(error);
    }
  };

  function getLastDateOfCurrentYear() {
    var today = new Date(); // Get current date
    var currentYear = today.getFullYear(); // Get current year
    var lastDate = new Date(currentYear, 11, 31); // Set to December 31st of the current year
    return formatDateToYYYYMMDD(lastDate);
  }

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    setSelectedImages([...selectedImages, ...Array.from(files)]);
  };

  const deleteImageFiles = async (index, url) => {
    try {
      const updatedImages = [...selectedImages];
      updatedImages.splice(index, 1);

      const formData = new FormData();
      formData.append("url", url);

      const response = await axios.post(
        `${API}/api/course/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      const resp = response?.data;
      setSelectedImages(updatedImages);
      // console.log(resp);
    } catch (error) {
      console.log(error);
    }
  };

  const handleRadioChange = (e) => {
    const value = e.target.value === "yes";
    setIsItCamp(value);
  };

  const getTime = (time) => {
    const tempdate = new Date(time);

    const hours = tempdate.getHours(); // Get the hour component (0-23)
    const minutes = tempdate.getMinutes(); // Get the minute component (0-59)


    // Format the time as needed (e.g., to a string with leading zeros)
    const formattedTime = `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}`;

    return formattedTime;
  };

  const checkAvailableSlots = async () => {
    try {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      let lastDateOfYear = getLastDateOfCurrentYear();

      const startDateTime = `${startDate}T${getTime(timeFrom)}:00`;
      const endDateTime = `${endDate ? endDate : lastDateOfYear}T${getTime(
        timeTo
      )}:00`;

      let raw = JSON.stringify({
        dates: {
          startDate: startDateTime,
          endDate: endDateTime,
          startTime: getTime(timeFrom),
          endTime: getTime(timeTo),
          days: selectedDays,
        },
      });

      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: raw,
      };

      const response = await fetch(`/api/check_slots`, requestOptions);

      const result = await response.json();


      if (result.message == "Slots available") {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.log(error);
      setBtnIsLoading(false);
      setShow2(true);
      setTimeout(() => {
        setShow2(false);
      }, 3000);
    }
  };

  // ### Final Save ###
  const saveHandler = async (e) => {
    // let data = await checkAvailableSlots();
    // console.log(data);
    try {
      e.preventDefault();
      setBtnIsLoading(true);

      let lastDateOfYear = getLastDateOfCurrentYear();
      lastDateOfYear = formatDateToYYYYMMDD(lastDateOfYear);
      if (sessionType?.name === "Individual") {
        setMaximumGroupSize(1);
      }
      if (!selectedImages.length > 0) {
        setShow2(true);
        setMessage("Plesae select atlst 1 image");
        setTimeout(() => {
          setShow2(false);
        }, 3000);
        setBtnIsLoading(false);
        return;
      }
      if (
        courseName !== "" &&
        (isItCamp ? campName !== "" : true) &&
        selectedDays.length > 0
      ) {
        const startDateTime = `${startDate}T${getTime(timeFrom)}:00`;
        const endDateTime = `${endDate ? endDate : lastDateOfYear}T${getTime(
          timeTo
        )}:00`;

        let isAvailable = await checkAvailableSlots();


        if (!isAvailable) {
          setBtnIsLoading(false);
          setShow2(true);
          setMessage("Slots are already booked");
          setTimeout(() => {
            setShow2(false);
          }, 3000);
          return;
        }

        let myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");

        let raw = JSON.stringify({
          courseName: `${courseName.replace(/\s+/g, " ").trim()}`,
          description: `${courseDescription}`,
          images: imageUrl,
          coach_id: "",
          coachName: "",
          category: `${categoryType?.name}`,
          sessionType:
            classType == "course" ? `${sessionType?.value}` : "individual",
          classType: classType,
          camp: isItCamp,
          campName: `${campName.replace(/\s+/g, " ").trim()}`,
          fees: {
            feesCourse: classType == "class" ? "" : price,
            fees30: classType == "class" ? fees30 : "",
            fees60: classType == "class" ? fees60 : "",
          },
          maxGroupSize: maximumGroupSize !== "" ? maximumGroupSize : 1,
          amenitiesProvided: courseAmeneties,
          proficiency: `${proficiencyLevel?.value}`,
          facility: courseFacility,
          coachEmail: coach.email,
          dates: {
            startDate: startDateTime,
            endDate: endDateTime,
            startTime: getTime(timeFrom),
            endTime: getTime(timeTo),
            days: selectedDays,
          },
          whatYouHaveToBring: carryThings,
          cancellationPolicy: cancellationPolicy,
        });
        let requestOptions = {
          method: "POST",
          headers: myHeaders,
          body: raw,
        };
        console.log(raw);

        const response = await fetch(`/api/create_course`, requestOptions);

        const result = await response.json();

        setBtnIsLoading(false);

        if (!result.error) {
          setBtnIsLoading(false);
          setCourseAmenetiesError(false);
          setCourseDescriptionError(false);
          setShow(true);
          setCourseName("");
          setCourseNameError(false);
          setCampName("");
          setCampNameError(false);
          setMaximumGroupSize("");
          setMaximumGroupSizeError(false);
          setCourseDescription("");
          setCourseAmeneties("");
          setStartDateError(false);
          setEndDate("");
          setPriceError(false);
          setFees30("");
          setFees60("");
          setStartDate("");
          setTimeFrom("");
          setSelectedDaysError(false);
          setSelectedDays([]);
          setCancellationPolicy("");
          setCarryThings("");
          setCategoryType({});
          setCourseFacility({});
          setTimeTo("");
          setFees30Error(false);
          setFees60Error(false);
          setSelectedImages([]);
          setCategoryError(false);
          setFacilityError(false);
          setTimeout(() => {
            setShow(false);
          }, 3000);
          router.push("/course/list");
        } else {
          setBtnIsLoading(false);
          setShow2(true);
          setTimeout(() => {
            setShow2(false);
          }, 3000);
        }
      } else {
        setShow2(true);
        setMessage("Please fill all the fields");
        setTimeout(() => {
          setShow2(false);
        }, 3000);
        if (courseName.length < 3) {
          setCourseNameError(true);
        }
        if (maximumGroupSize.length === 0) {
          setMaximumGroupSizeError(true);
        }
        if (courseAmeneties.length === 0 || courseAmeneties === "<p><br></p>") {
          setCourseAmenetiesError(true);
        }
        if (price === "") {
          setPriceError(true);
        }
        if (fees30 == "") {
          setFees30Error(true);
        }

        if (fees60 === "") {
          setFees60Error(true);
        }
        if (timeFrom == "") {
          setTimeFromError(true);
        }
        if (selectedDays.length == 0) {
          setSelectedDaysError(true);
        }
        if (courseFacility.name == "" || !courseFacility?.name) {
          setFacilityError(true);
        }
        if (categoryType.name == "" || !categoryType?.name) {
          setCategoryError(true);
        }
        if (timeFrom == "") {
          setTimeToError(true);
        }
        if (courseDescription === "" || courseDescription === "<p><br></p>") {
          setCourseDescriptionError(true);
        }
        if (campName.length === 0 && isItCamp) {
          setCampNameError(true);
        }
        if (startDate === "") {
          setStartDateError(true);
        }
        setBtnIsLoading(false);
      }
    } catch (error) {
      setBtnIsLoading(false);
      setShow2(true);
      setTimeout(() => {
        setShow2(false);
      }, 3000);
    }
  };

  function formatDateToYYYYMMDD(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed

    return `${year}-${month}-${day}`;
  }

  return (
    <>
      <div className="w-full lg:w-2/3 md:2/3 m-auto ">
        <form onSubmit={handleSubmit}>
          <div className="px-4 py-6 sm:p-8">
            <div className="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
              {/* Course name */}
              <div className="col-span-full">
                <label
                  htmlFor="course-name"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Course Name
                </label>
                <div className="mt-2">
                  <input
                    type="text"
                    name="course-name"
                    id="course-name"
                    value={courseName}
                    autoComplete="off"
                    className={
                      courseNameError
                        ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                        : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                    }
                    placeholder="Enter course name"
                    onChange={(e) => {
                      setCourseName(e.target.value);
                      if (e.target.value !== "" && e.target.value.length >= 3) {
                        setCourseNameError(false);
                      } else {
                        setCourseNameError(true);
                      }
                    }}
                  />
                  {courseNameError && (
                    <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                      Please enter course name with atleast 3 characters
                    </span>
                  )}
                </div>
              </div>

              {/* Description */}
              <div className="col-span-full ">
                <label
                  htmlFor="description"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Description
                </label>
                <div
                  className={
                    courseDescriptionError
                      ? "mt-2 bg-white min-h-10 rounded-sm border-2 border-rose-500 text-gray-900 shadow-sm"
                      : "mt-2 bg-white min-h-10 rounded-md border border-gray-300 text-gray-900 shadow-sm"
                  }
                >
                  <ReactQuill
                    value={courseDescription}
                    onChange={(value) => {
                      setCourseDescription(value);
                      if (value === "" || value === "<p><br></p>") {
                        setCourseDescriptionError(true);
                      } else {
                        setCourseDescriptionError(false);
                      }
                    }}
                  />
                </div>
                {courseDescriptionError && (
                  <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                    Please enter description
                  </span>
                )}
              </div>
              {/* Upload Image */}
              <div
                className="col-span-full bg-white p-3 rounded-sm"
                onDragOver={handleDragOver}
                onDrop={handleDrop}
              >
                <div className="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-12 text-center hover:border-gray-400">
                  <label
                    htmlFor="imageInput"
                    className="cursor-pointer flex flex-col items-center"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {/* SVG icon */}
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="w-14 h-14"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                      />
                    </svg>

                    <span className="mt-2 block text-sm text-gray-500">
                      <span className="font-semibold text-blue-500">
                        Upload a file
                      </span>{" "}
                      or drag and drop <br /> PNG, JPG, GIF up to 10MB
                    </span>
                    <input
                      id="imageInput"
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handleFileChange}
                      className="hidden"
                    />
                  </label>
                  <div>
                    {selectedImages.length > 0 && (
                      <div className="mt-4 grid grid-cols-3 gap-4">
                        {selectedImages.map((image, index) => (
                          <div key={`image_${index}`} className="relative">
                            <img
                              src={image.url}
                              alt={`Selected Image ${index}`}
                              className="w-full h-full object-cover rounded"
                            />
                            <button
                              onClick={(e) => {
                                deleteImageFiles(index, image.url);
                              }}
                              className="absolute top-2 right-2 bg-white p-1 rounded-full hover:bg-gray-200 focus:outline-none z-10"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                                className="h-4 w-4 text-red-500"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="M6 18L18 6M6 6l12 12"
                                />
                              </svg>
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Specific / Independent Classes */}
              <div className="col-span-full bg-white px-2 py-2 rounded-sm ">
                <div>
                  <div className="space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0 justify-around">
                    <div className="flex items-center">
                      <input
                        // id={notificationMethod.id}
                        name="notification-method"
                        type="radio"
                        onChange={(e) =>
                          e.target.checked && setClassType("class")
                        }
                        defaultChecked={classType == "class"}
                        className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                      />
                      <label
                        // htmlFor={notificationMethod.id}
                        className="ml-3 block text-sm font-medium leading-6 text-gray-900"
                      >
                        {"Session"}
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        // id={notificationMethod.id}
                        name="notification-method"
                        type="radio"
                        onChange={(e) =>
                          e.target.checked && setClassType("course")
                        }
                        defaultChecked={classType == "course"}
                        className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                      />
                      <label
                        // htmlFor={notificationMethod.id}
                        className="ml-3 block text-sm font-medium leading-6 text-gray-900"
                      >
                        {"Course"}
                      </label>
                    </div>
                  </div>

                  <div>
                    <div className="mt-3 justify-items-center">
                      {/*date*/}
                      <div className="col-span-full">
                        <label
                          htmlFor="start-date"
                          className="block text-sm font-medium leading-6 text-gray-900"
                        >
                          Start Date
                        </label>
                        <div className="mt-2">
                          <input
                            type="date"
                            name="start-date"
                            id="start-date"
                            value={startDate}
                            min={formatDateToYYYYMMDD(new Date())}
                            autoComplete="off"
                            className={
                              startDateError
                                ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                                : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                            }
                            // placeholder="Enter course name"
                            onChange={(e) => {
                              setStartDate(e.target.value);
                              if (e.target.value !== "") {
                                setStartDateError(false);
                              } else {
                                setStartDateError(true);
                              }
                            }}
                          />
                          {startDateError && (
                            <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                              Please select the start date
                            </span>
                          )}
                        </div>
                      </div>
                      {/*days*/}
                      <div className="mt-4">
                        Select the Days
                        <div className="grid grid-cols-7 text-center text-xs leading-6 text-gray-500">
                          {daysOfWeek.map((day, dayIdx) => (
                            <button
                              key={dayIdx}
                              type="button"
                              onClick={() => {
                                setSelectedDays(
                                  selectedDays.includes(day)
                                    ? selectedDays.filter(
                                        (selected) => selected !== day
                                      )
                                    : [...selectedDays, day]
                                );
                                if (!day) {
                                  setSelectedDaysError(true);
                                } else {
                                  setSelectedDaysError(false);
                                }
                              }}
                              className={`${
                                selectedDays.includes(day)
                                  ? "bg-blue-500 text-white"
                                  : ""
                              } p-2 rounded-full`}
                            >
                              {day}
                            </button>
                          ))}
                        </div>
                        {selectedDaysError && (
                          <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                            Select the Days
                          </span>
                        )}
                      </div>

                      {/* end date */}
                      <div className="mt-4">
                        <label
                          htmlFor="end_date"
                          className="block mb-2 text-sm font-medium text-gray-900 dark:text-black"
                        >
                          End Date:
                        </label>
                        <ul className="items-center w-full text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg sm:flex dark:border-gray-600 dark:text-white">
                          <li className="w-full border-b border-gray-200 sm:border-b-0 sm:border-r dark:border-gray-600">
                            <div className="flex items-center ps-3">
                              <input
                                id="horizontal-list-radio-license"
                                type="radio"
                                required
                                value=""
                                defaultChecked={!isEnd}
                                onChange={(e) =>
                                  e.target.checked
                                    ? setIsEnd(false)
                                    : setIsEnd(true)
                                }
                                name="list-radio"
                                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300  dark:bg-gray-600 dark:border-gray-500"
                              />
                              <label
                                htmlFor="horizontal-list-radio-license"
                                className="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-black-300"
                              >
                                Never
                              </label>
                            </div>
                          </li>

                          <li className="w-full border-b border-gray-200 sm:border-b-0 sm:border-r dark:border-gray-600">
                            <div className="flex items-center ps-3">
                              <input
                                id="horizontal-list-radio-license"
                                type="radio"
                                required
                                value=""
                                defaultChecked={isEnd}
                                onChange={(e) =>
                                  e.target.checked
                                    ? setIsEnd(true)
                                    : setIsEnd(false)
                                }
                                name="list-radio"
                                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300  dark:bg-gray-600 dark:border-gray-500"
                              />
                              <label
                                htmlFor="horizontal-list-radio-license"
                                className="w-full py-3 ms-2 text-sm font-medium text-gray-900 dark:text-black-300"
                              >
                                On
                              </label>
                            </div>
                          </li>
                        </ul>
                      </div>

                      {isEnd && (
                        <div className="mt-3">
                          <label
                            htmlFor="start_date"
                            className="block mb-2 text-sm font-medium text-gray-900 dark:text-black"
                          >
                            End Date:
                          </label>
                          <input
                            type="date"
                            min={formatDateToYYYYMMDD(startDate)}
                            id="start_date"
                            onChange={(e) => setEndDate(e.target.value)}
                            required
                            className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  dark:border-gray-600 dark:placeholder-gray-400 dark:text-black dark:focus:ring-blue-500 dark:focus:border-blue-500"
                          />
                        </div>
                      )}

                      {/* time slots */}
                      <div className="flex flex-row justify-between mt-6">
                        <div>
                          <h1 className="text-lg font-medium tracking-wide">
                            Start Time:
                          </h1>
                          <DatePicker
                            selected={timeFrom}
                            className={
                              timeFromError
                                ? "my-custom-datepicker dateError"
                                : "my-custom-datepicker"
                            }
                            onChange={(value) => {
                              if (value == "") {
                                setTimeFromError(true);
                              } else {
                                setTimeFromError(false);
                              }
                              setTimeFrom(value);
                              setTimeTo("");
                            }}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={10}
                            timeCaption="Time"
                            dateFormat="h:mm aa"
                            popperPlacement="left-start"
                            placeholderText="Select time"
                            disabled={startDate == ""}
                            minTime={minTime || new Date(0, 0, 0, 0, 0)}
                            maxTime={new Date(0, 0, 0, 23, 59)}
                            // excludeTimes={excludedSlots || []}
                          />
                          <br />
                          {timeFromError && (
                            <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                              Select the time
                            </span>
                          )}
                        </div>

                        <div>
                          <h1 className="text-lg font-medium tracking-wide">
                            End Time:
                          </h1>
                          <DatePicker
                            selected={timeTo}
                            onChange={(value) => {
                              setTimeTo(value);
                              if (value == "") {
                                setTimeToError(true);
                              } else {
                                setTimeToError(false);
                              }
                            }}
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={10}
                            timeCaption="Time"
                            dateFormat="h:mm aa"
                            className={
                              timeToError
                                ? "my-custom-datepicker dateError"
                                : "my-custom-datepicker"
                            }
                            popperPlacement="left-start"
                            placeholderText="Select time"
                            disabled={timeFrom === ""}
                            minTime={getMinTime()}
                            filterTime={filterPassedTime}
                            maxTime={new Date(0, 0, 0, 23, 59)}
                            // excludeTimes={excludedSlots || []}
                          />
                          <br />
                          {timeToError && (
                            <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                              Select the time
                            </span>
                          )}
                        </div>
                      </div>

                      {/*Price*/}

                      {classType === "class" ? (
                        <div className="mt-3 flex flex-row justify-between">
                          <div>
                            <label
                              htmlFor="start_date"
                              className="block mb-2 text-sm font-medium text-gray-900 dark:text-black"
                            >
                              Price:
                            </label>
                            30 Min
                            <input
                              type="number"
                              name="price"
                              id="price"
                              placeholder="₹0.00"
                              value={fees30}
                              autoComplete="off"
                              className={
                                fees30Error
                                  ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                                  : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                              }
                              // placeholder="Enter course name"
                              onChange={(e) => {
                                setFees30(e.target.value);
                                if (e.target.value !== "") {
                                  setFees30Error(false);
                                } else {
                                  setFees30Error(true);
                                }
                              }}
                            />
                            <style jsx>{`
                              input[type="number"]::-webkit-inner-spin-button,
                              input[type="number"]::-webkit-outer-spin-button {
                                -webkit-appearance: none;
                                margin: 0;
                              }

                              input[type="number"] {
                                -moz-appearance: textfield;
                              }
                            `}</style>
                            {fees30Error && (
                              <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                                Please enter the price
                              </span>
                            )}
                          </div>



                          <div>
                            <label
                              htmlFor="start_date"
                              className="block mb-2 text-sm font-medium text-gray-900 dark:text-black"
                            >
                              Price:
                            </label>
                            60 Min
                            <input
                              type="number"
                              name="price"
                              id="price"
                              placeholder="₹0.00"
                              value={fees60}
                              autoComplete="off"
                              className={
                                fees60Error
                                  ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                                  : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                              }
                              // placeholder="Enter course name"
                              onChange={(e) => {
                                setFees60(e.target.value);
                                if (e.target.value !== "") {
                                  setFees60Error(false);
                                } else {
                                  setFees60Error(true);
                                }
                              }}
                            />
                            <style jsx>{`
                              input[type="number"]::-webkit-inner-spin-button,
                              input[type="number"]::-webkit-outer-spin-button {
                                -webkit-appearance: none;
                                margin: 0;
                              }

                              input[type="number"] {
                                -moz-appearance: textfield;
                              }
                            `}</style>
                            {fees60Error && (
                              <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                                Please enter the price
                              </span>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className="col-span- mt-3">
                          <label
                            htmlFor="price"
                            className="block text-sm font-medium leading-6 text-gray-900"
                          >
                            Price
                          </label>
                          <div className="mt-2">
                            <input
                              type="number"
                              name="price"
                              id="price"
                              placeholder="₹0.00"
                              value={price}
                              autoComplete="off"
                              className={
                                priceError
                                  ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                                  : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                              }
                              // placeholder="Enter course name"
                              onChange={(e) => {
                                setPrice(e.target.value);
                                if (e.target.value !== "") {
                                  setPriceError(false);
                                } else {
                                  setPriceError(true);
                                }
                              }}
                            />
                            <style jsx>{`
                              input[type="number"]::-webkit-inner-spin-button,
                              input[type="number"]::-webkit-outer-spin-button {
                                -webkit-appearance: none;
                                margin: 0;
                              }

                              input[type="number"] {
                                -moz-appearance: textfield;
                              }
                            `}</style>
                            {priceError && (
                              <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                                Please enter the price
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                {/* <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                  <button
                    type="button"
                    className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2"
                    onClick={() => {
                      const datesAndTime = selectedDates?.map((date) =>
                        date.toLocaleString("en-IN")
                      );

                      const timeConverter = (data) => {
                        const currentDate = new Date(data);
                        const options = {
                          hour: "numeric",
                          minute: "numeric",
                          hour12: true,
                        };
                        return currentDate.toLocaleTimeString("en-IN", options);
                      };
                      const finalDateWithTime = datesAndTime.map((x) => {
                        return {
                          id: uuidv4(),
                          date: x,
                          duration: "",
                          timeFrom: timeConverter(timeFrom),
                          timeTo: timeConverter(timeTo),
                        };
                      });
                      if (timeConverter(timeFrom) === timeConverter(timeTo)) {
                        setOpen(true);
                      } else {
                        setSelectedDatesAndTime(finalDateWithTime);
                        setOpen(false);
                        setTimeFrom("");
                        setTimeTo("");
                      }
                    }}
                  >
                    Save
                  </button>
                 
                </div>     */}
              </div>
              {classType === "course" && (
                <div className="col-span-full">
                  <Listbox
                    value={sessionType}
                    onChange={(e) => {
                      setSessionType(sessionTypeOption[e.id]);
                      setMaximumGroupSize(1);
                    }}
                  >
                    {({ open }) => (
                      <>
                        <Listbox.Label className="block text-sm font-medium leading-6 text-gray-900">
                          Session Type
                        </Listbox.Label>
                        <div className="relative mt-2">
                          <Listbox.Button className="relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-sky-500 sm:text-sm sm:leading-6">
                            <span className="block truncate">
                              {sessionType.name}
                            </span>
                            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                              <ChevronUpDownIcon
                                className="h-5 w-5 text-gray-400"
                                aria-hidden="true"
                              />
                            </span>
                          </Listbox.Button>

                          <Transition
                            show={open}
                            as={Fragment}
                            leave="transition ease-in duration-100"
                            leaveFrom="opacity-100"
                            leaveTo="opacity-0"
                          >
                            <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                              {sessionTypeOption.map((session) => (
                                <Listbox.Option
                                  key={session.id}
                                  className={({ active }) =>
                                    classNames(
                                      active
                                        ? "bg-indigo-600 text-white"
                                        : "text-gray-900",
                                      "relative cursor-default select-none py-2 pl-3 pr-9"
                                    )
                                  }
                                  value={session}
                                >
                                  {({ selected, active }) => (
                                    <>
                                      <span
                                        className={classNames(
                                          selected
                                            ? "font-semibold"
                                            : "font-normal",
                                          "block truncate"
                                        )}
                                      >
                                        {session.name}
                                      </span>

                                      {selected ? (
                                        <span
                                          className={classNames(
                                            active
                                              ? "text-white"
                                              : "text-indigo-600",
                                            "absolute inset-y-0 right-0 flex items-center pr-4"
                                          )}
                                        >
                                          <CheckIcon
                                            className="h-5 w-5"
                                            aria-hidden="true"
                                          />
                                        </span>
                                      ) : null}
                                    </>
                                  )}
                                </Listbox.Option>
                              ))}
                            </Listbox.Options>
                          </Transition>
                        </div>
                      </>
                    )}
                  </Listbox>
                </div>
              )}

              {/* ------ */}

              {/* Is it a camp */}
              {classType !== "class" && (
                <div className="col-span-full">
                  <div className="flex gap-4 items-center">
                    <label
                      htmlFor="camp-name"
                      className="block text-sm font-medium leading-6 text-gray-900"
                    >
                      Is it a camp?
                    </label>
                    <div className="flex items-center gap-1">
                      <input
                        type="radio"
                        id="yes"
                        name="camp-type"
                        value="yes"
                        onChange={handleRadioChange}
                      />
                      <label htmlFor="yes" className="text-sm text-gray-700">
                        Yes
                      </label>
                    </div>
                    <div className="flex items-center gap-1">
                      <input
                        type="radio"
                        id="no"
                        name="camp-type"
                        value="no"
                        onChange={handleRadioChange}
                        defaultChecked
                      />
                      <label htmlFor="no" className="text-sm text-gray-700">
                        No
                      </label>
                    </div>
                  </div>
                  {isItCamp && (
                    <div className="mt-2">
                      <input
                        type="text"
                        name="camp-name"
                        id="camp-name"
                        value={campName}
                        autoComplete="off"
                        className={
                          campNameError
                            ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                            : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                        }
                        placeholder="Enter camp name"
                        onChange={(e) => {
                          setCampName(e.target.value);
                          if (e.target.value !== "") {
                            setCampNameError(false);
                          } else {
                            setCampNameError(true);
                          }
                        }}
                      />
                      {campNameError && (
                        <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                          Please enter camp name
                        </span>
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* Category */}
              <div className="col-span-full">
                <Listbox
                  value={categoryType}
                  onChange={(e) => {
                    console.log(e);
                    if (!e.name) {
                      setCategoryError(true);
                    } else {
                      setCategoryError(false);
                    }
                    setCategoryType(e);
                  }}
                >
                  {({ open }) => (
                    <>
                      <Listbox.Label className="block text-sm font-medium leading-6 text-gray-900">
                        Category
                      </Listbox.Label>
                      <div className="relative mt-2">
                        <Listbox.Button
                          className={
                            categoryError
                              ? "mt-1 block w-full px-3 rounded-md border-2 border-rose-500  cursor-default bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-sky-500 sm:text-sm sm:leading-6"
                              : "relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-sky-500 sm:text-sm sm:leading-6"
                          }
                        >
                          <span className="block truncate">
                            {categoryType.name
                              ? categoryType.name
                              : "Select a category"}
                          </span>
                          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                            <ChevronUpDownIcon
                              className="h-5 w-5 text-gray-400"
                              aria-hidden="true"
                            />
                          </span>
                        </Listbox.Button>

                        <Transition
                          show={open}
                          as={Fragment}
                          leave="transition ease-in duration-100"
                          leaveFrom="opacity-100"
                          leaveTo="opacity-0"
                        >
                          <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                            {categories &&
                              categories.length > 0 &&
                              categories.map((category) => (
                                <Listbox.Option
                                  key={category.id}
                                  className={({ active }) =>
                                    classNames(
                                      active
                                        ? "bg-indigo-600 text-white"
                                        : "text-gray-900",
                                      "relative cursor-default select-none py-2 pl-3 pr-9"
                                    )
                                  }
                                  value={category}
                                >
                                  {({ selected, active }) => (
                                    <>
                                      <span
                                        className={classNames(
                                          selected
                                            ? "font-semibold"
                                            : "font-normal",
                                          "block truncate"
                                        )}
                                      >
                                        {category.name}
                                      </span>

                                      {selected ? (
                                        <span
                                          className={classNames(
                                            active
                                              ? "text-white"
                                              : "text-indigo-600",
                                            "absolute inset-y-0 right-0 flex items-center pr-4"
                                          )}
                                        >
                                          <CheckIcon
                                            className="h-5 w-5"
                                            aria-hidden="true"
                                          />
                                        </span>
                                      ) : null}
                                    </>
                                  )}
                                </Listbox.Option>
                              ))}
                          </Listbox.Options>
                        </Transition>
                      </div>
                    </>
                  )}
                </Listbox>
                {categoryError && (
                  <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                    Please select the category
                  </span>
                )}
              </div>
              {/* ----- */}
              {/* Select Facility */}
              <div className="col-span-full">
                <Listbox
                  value={courseFacility}
                  onChange={(e) => {
                    if (!e.name) {
                      setFacilityError(true);
                    } else {
                      setFacilityError(false);
                    }
                    console.log(e);
                    setCourseFacility(e);
                    setCourseAmeneties(e.amenities);
                  }}
                >
                  {({ open }) => (
                    <>
                      <Listbox.Label className="block text-sm font-medium leading-6 text-gray-900">
                        Select Facility
                      </Listbox.Label>
                      <div className="relative mt-2">
                        <Listbox.Button
                          className={
                            facilityError
                              ? "mt-1 block w-full px-3 rounded-md border-2 border-rose-500  cursor-default bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-sky-500 sm:text-sm sm:leading-6"
                              : "relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-sky-500 sm:text-sm sm:leading-6"
                          }
                        >
                          <span className="block truncate">
                            {courseFacility.name
                              ? courseFacility.name
                              : "Select the facility"}
                          </span>
                          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                            <ChevronUpDownIcon
                              className="h-5 w-5 text-gray-400"
                              aria-hidden="true"
                            />
                          </span>
                        </Listbox.Button>

                        <Transition
                          show={open}
                          as={Fragment}
                          leave="transition ease-in duration-100"
                          leaveFrom="opacity-100"
                          leaveTo="opacity-0"
                        >
                          <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                            {facilities.map((facility) => (
                              <Listbox.Option
                                key={facility._id}
                                className={({ active }) =>
                                  classNames(
                                    active
                                      ? "bg-indigo-600 text-white"
                                      : "text-gray-900",
                                    "relative cursor-default select-none py-2 pl-3 pr-9"
                                  )
                                }
                                value={facility}
                              >
                                {({ selected, active }) => (
                                  <>
                                    <span
                                      className={classNames(
                                        selected
                                          ? "font-semibold"
                                          : "font-normal",
                                        "block truncate"
                                      )}
                                    >
                                      {facility.name}
                                    </span>

                                    {selected ? (
                                      <span
                                        className={classNames(
                                          active
                                            ? "text-white"
                                            : "text-indigo-600",
                                          "absolute inset-y-0 right-0 flex items-center pr-4"
                                        )}
                                      >
                                        <CheckIcon
                                          className="h-5 w-5"
                                          aria-hidden="true"
                                        />
                                      </span>
                                    ) : null}
                                  </>
                                )}
                              </Listbox.Option>
                            ))}
                          </Listbox.Options>
                        </Transition>
                      </div>
                    </>
                  )}
                </Listbox>
                {facilityError && (
                  <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                    Please select the facility
                  </span>
                )}
              </div>

              {/* Maximum Group Size */}
              {sessionType?.name === "Group" && classType == "course" && (
                <div className="col-span-full">
                  <label
                    htmlFor="max-group-size"
                    className="block text-sm font-medium leading-6 text-gray-900"
                  >
                    Maximum Group Size
                  </label>
                  <div className="mt-2">
                    <input
                      type="number"
                      name="max-group-size"
                      id="max-group-size"
                      value={maximumGroupSize}
                      className={
                        maximumGroupSizeError
                          ? "mt-1 block w-full px-3 py-2  rounded-md border-2 border-rose-500  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                          : "mt-1 block w-full px-3 py-2  rounded-md border-0  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-sky-500 sm:text-sm sm:leading-6"
                      }
                      placeholder="Enter max group size"
                      onChange={(e) => {
                        setMaximumGroupSize(
                          e.target.value.replace(/\s+/g, " ").trim()
                        );
                        if (e.target.value !== "") {
                          setMaximumGroupSizeError(false);
                        } else {
                          setMaximumGroupSizeError(true);
                        }
                      }}
                    />
                    <style jsx>{`
                      input[type="number"]::-webkit-inner-spin-button,
                      input[type="number"]::-webkit-outer-spin-button {
                        -webkit-appearance: none;
                        margin: 0;
                      }

                      input[type="number"] {
                        -moz-appearance: textfield;
                      }
                    `}</style>
                    {maximumGroupSizeError && (
                      <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                        Please enter course name
                      </span>
                    )}
                  </div>
                </div>
              )}
              {/* Proficiency Level */}
              <div className="col-span-full">
                <Listbox
                  value={proficiencyLevel}
                  onChange={(e) =>
                    setProficiencyLevel(proficiencyLevelOption[e.id])
                  }
                >
                  {({ open }) => (
                    <>
                      <Listbox.Label className="block text-sm font-medium leading-6 text-gray-900">
                        Proficiency Level
                      </Listbox.Label>
                      <div className="relative mt-2">
                        <Listbox.Button className="relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-sky-500 sm:text-sm sm:leading-6">
                          <span className="block truncate">
                            {proficiencyLevel.name}
                          </span>
                          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                            <ChevronUpDownIcon
                              className="h-5 w-5 text-gray-400"
                              aria-hidden="true"
                            />
                          </span>
                        </Listbox.Button>

                        <Transition
                          show={open}
                          as={Fragment}
                          leave="transition ease-in duration-100"
                          leaveFrom="opacity-100"
                          leaveTo="opacity-0"
                        >
                          <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                            {proficiencyLevelOption.map((proficiency) => (
                              <Listbox.Option
                                key={proficiency.id}
                                className={({ active }) =>
                                  classNames(
                                    active
                                      ? "bg-indigo-600 text-white"
                                      : "text-gray-900",
                                    "relative cursor-default select-none py-2 pl-3 pr-9"
                                  )
                                }
                                value={proficiency}
                              >
                                {({ selected, active }) => (
                                  <>
                                    <span
                                      className={classNames(
                                        selected
                                          ? "font-semibold"
                                          : "font-normal",
                                        "block truncate"
                                      )}
                                    >
                                      {proficiency.name}
                                    </span>

                                    {selected ? (
                                      <span
                                        className={classNames(
                                          active
                                            ? "text-white"
                                            : "text-indigo-600",
                                          "absolute inset-y-0 right-0 flex items-center pr-4"
                                        )}
                                      >
                                        <CheckIcon
                                          className="h-5 w-5"
                                          aria-hidden="true"
                                        />
                                      </span>
                                    ) : null}
                                  </>
                                )}
                              </Listbox.Option>
                            ))}
                          </Listbox.Options>
                        </Transition>
                      </div>
                    </>
                  )}
                </Listbox>
              </div>
              {/* Ameneties */}
              <div className="col-span-full ">
                <label
                  htmlFor="description"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Ameneties
                </label>
                <div
                  className={
                    // courseAmenetiesError
                    //   ? "mt-2 bg-white min-h-10 rounded-sm border-2 border-rose-500 text-gray-900 shadow-sm":
                    "mt-2 bg-white min-h-10 rounded-md border border-gray-300 text-gray-900 shadow-sm"
                  }
                >
                  <ReactQuill
                    value={courseAmeneties}
                    onChange={(value) => {
                      setCourseAmeneties(value);
                      // if (value !== "" && value !== "<p><br></p>") {
                      //   setCourseAmenetiesError(false);
                      // } else {
                      //   if (value === "<p><br></p>") {
                      //     setCourseAmenetiesError(false);
                      //     setCourseAmeneties("");
                      //   } else {
                      //     setCourseAmenetiesError(true);
                      //   }
                      // }
                    }}
                  />
                </div>
                {/* {courseAmenetiesError && (
                  <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                    Please enter ameneties
                  </span>
                )} */}
              </div>
              {/*whatyouhavetobring*/}
              <div className="col-span-full ">
                <label
                  htmlFor="carryThings"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Thing Have to Carry with
                </label>
                <div
                  className={
                    // courseDescriptionError
                    //   ? "mt-2 bg-white min-h-10 rounded-sm border-2 border-rose-500 text-gray-900 shadow-sm" :
                    "mt-2 bg-white min-h-10 rounded-md border border-gray-300 text-gray-900 shadow-sm"
                  }
                >
                  <ReactQuill
                    value={carryThings}
                    onChange={(value) => {
                      setCarryThings(value);
                    }}
                  />
                </div>
                {/* {courseDescriptionError && (
                  <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                    Please enter description
                  </span>
                )} */}
              </div>
              {/*cancellationPolicy*/}
              <div className="col-span-full ">
                <label
                  htmlFor="cancellationPolicy"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Cancellation Policy
                </label>
                <div
                  className={
                    // courseDescriptionError
                    //   ? "mt-2 bg-white min-h-10 rounded-sm border-2 border-rose-500 text-gray-900 shadow-sm":
                    "mt-2 bg-white min-h-10 rounded-md border border-gray-300 text-gray-900 shadow-sm"
                  }
                >
                  <ReactQuill
                    value={cancellationPolicy}
                    onChange={(value) => {
                      setCancellationPolicy(value);
                    }}
                  />
                </div>
                {/* {courseDescriptionError && (
                  <span className="mt-2 text-sm text-red-500 peer-[&:not(:placeholder-shown):not(:focus):invalid]:block">
                    Please enter description
                  </span>
                )} */}
              </div>
            </div>
          </div>
          <div className="flex items-center justify-end gap-x-6 border-t border-gray-900/10 px-4 py-4 sm:px-8">
            <button
              type="button"
              onClick={() => router.push("/calendar")}
              className=" rounded-md bg-black px-6 py-2.5 text-sm  text-white shadow-sm hover:bg-slate-950 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-slate-950"
            >
              Cancel
            </button>
            {btnIsLoading ? (
              <button
                disabled
                type="button"
                className="text-white px-3 py-2.5 bg-indigo-700 hover:bg-blue-800   font-medium rounded text-sm text-center mr-2 dark:bg-indigo-600 dark:hover:bg-indigo-700  inline-flex items-center"
              >
                <svg
                  aria-hidden="true"
                  role="status"
                  class="inline mr-3 w-4 h-4 text-white animate-spin"
                  viewBox="0 0 100 101"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                    fill="#E5E7EB"
                  ></path>
                  <path
                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                    fill="currentColor"
                  ></path>
                </svg>
                Loading...
              </button>
            ) : (
              <button
                type="submit"
                className="rounded-md bg-indigo-600 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                onClick={saveHandler}
              >
                Save
              </button>
            )}
          </div>
        </form>

        {/* Notification on successfully Course Creation */}
        <div
          aria-live="assertive"
          className="pointer-events-none fixed inset-0 flex items-center justify-center px-4 py-6 sm:items-start sm:p-6 z-50"
        >
          <div className="flex w-full flex-col items-center space-y-4 sm:items-center">
            {/* Notification panel, dynamically insert this into the live region when it needs to be displayed */}
            <Transition
              show={show}
              as={Fragment}
              enter="transform ease-out duration-300 transition"
              enterFrom="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
              enterTo="translate-y-0 opacity-100 sm:translate-x-0"
              leave="transition ease-in duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-5">
                <div className="p-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <CheckCircleIcon
                        className="h-6 w-6 text-green-400"
                        aria-hidden="true"
                      />
                    </div>
                    <div className="ml-3 w-0 flex-1 pt-0.5">
                      <p className="text-sm font-medium text-gray-900">
                        Course Created Successfully
                      </p>
                    </div>
                    <div className="ml-4 flex flex-shrink-0">
                      <div
                        className="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                        onClick={() => {
                          setShow(false);
                        }}
                      >
                        <span className="sr-only">Close</span>
                        <XMarkIcon className="h-5 w-5" aria-hidden="true" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Transition>
          </div>
        </div>
        {/* Notification on Error while Course Creation */}
        <div
          aria-live="assertive"
          className="pointer-events-none fixed inset-0 flex items-center justify-center px-4 py-6 sm:items-start sm:p-6 z-50"
        >
          <div className="flex w-full flex-col items-center space-y-4 sm:items-center">
            {/* Notification panel, dynamically insert this into the live region when it needs to be displayed */}
            <Transition
              show={show2}
              as={Fragment}
              enter="transform ease-out duration-300 transition"
              enterFrom="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
              enterTo="translate-y-0 opacity-100 sm:translate-x-0"
              leave="transition ease-in duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="rounded-md bg-red-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <XCircleIcon
                      className="h-5 w-5 text-red-400"
                      aria-hidden="true"
                    />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      {message}
                    </h3>
                  </div>
                </div>
              </div>
            </Transition>
          </div>
        </div>
      </div>
    </>
  );
}