import React from "react";
import { useDisclosure } from "@chakra-ui/react";
import Layout from "../../layout/default";
import NewsUpdateModal from "./NewsUpdateModal";
import DeleteConfirmDialog from "./DeleteConfirmDialog";
import { useNewsUpdates } from "./hooks/useNewsUpdates";
import NewsHeader from "./components/NewsHeader";
import NewsTable from "./components/NewsTable";

const NewsUpdates = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const {
    newsUpdates, setNewsUpdates, loading, formData, setFormData, editingId,
    deleteId, setDeleteId, imageFile, setImageFile, submitting, adjustBtnEdit,
    setAdjustBtnEdit, previousNewsData, setPreviousNewsData, posBtnLoading,
    userData, handleSubmit, handleEdit, handleDelete, updateNewsPosition,
    handleCloseModal, handleDragStart, handleDragOver, handleDragEnd
  } = useNewsUpdates(onClose);

  const handleEditClick = (item) => {
    handleEdit(item);
    onOpen();
  };

  const handleDeleteClick = async () => {
    await handleDelete();
    onDeleteClose();
  };

  const handleModalClose = () => {
    handleCloseModal();
    onClose();
  };

  const openDeleteDialog = (id) => {
    setDeleteId(id);
    onDeleteOpen();
  };

  return (
    <Layout title="News & Updates" content="container">
      <NewsHeader
        adjustBtnEdit={adjustBtnEdit}
        userData={userData}
        setAdjustBtnEdit={setAdjustBtnEdit}
        setPreviousNewsData={setPreviousNewsData}
        newsUpdates={newsUpdates}
        onOpen={onOpen}
        setNewsUpdates={setNewsUpdates}
        previousNewsData={previousNewsData}
        posBtnLoading={posBtnLoading}
        updateNewsPosition={updateNewsPosition}
      />

      <NewsTable
        loading={loading}
        newsUpdates={newsUpdates}
        adjustBtnEdit={adjustBtnEdit}
        handleDragStart={handleDragStart}
        handleDragOver={handleDragOver}
        handleDragEnd={handleDragEnd}
        userData={userData}
        handleEdit={handleEditClick}
        openDeleteDialog={openDeleteDialog}
      />

      <NewsUpdateModal
        isOpen={isOpen}
        onClose={handleModalClose}
        formData={formData}
        setFormData={setFormData}
        editingId={editingId}
        imageFile={imageFile}
        setImageFile={setImageFile}
        onSubmit={handleSubmit}
        submitting={submitting}
      />

      <DeleteConfirmDialog
        isOpen={isDeleteOpen}
        onClose={onDeleteClose}
        onConfirm={handleDeleteClick}
        title="Delete News Update"
        message="Are you sure you want to delete this news update? This action cannot be undone."
      />
    </Layout>
  );
};

export default NewsUpdates;