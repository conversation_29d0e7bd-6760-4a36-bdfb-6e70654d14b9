
import { useEffect } from 'react';
import { useQuill } from 'react-quilljs';
import '../../../assets/scss/libs/editors/quill.scss';

// Controlled quill default component
export const Quill = ({ placeholderValue, value = '', onChange, height = 220, modules, formats }) => {
  const placeholder = placeholderValue ?? 'Write something...';
  const { quill, quillRef } = useQuill({ placeholder, modules, formats });

  useEffect(() => {
    if (!quill) return;

    // set initial content if provided
    const current = quill.root ? quill.root.innerHTML : '';
    if ((value || '') !== current) {
      quill.clipboard.dangerouslyPasteHTML(value || '');
    }

    const handler = () => {
      const html = quill.root ? quill.root.innerHTML : '';
      if (onChange) onChange(html);
    };

    quill.on('text-change', handler);
    return () => quill.off('text-change', handler);
  }, [quill, value, onChange]);

  return (
    <div style={{ width: '100%', height }}>
      <div ref={quillRef} />
    </div>
  );
};

// Minimal quill (keeps internal toolbar config)
export const QuillMinimal = ({ placeholderValue, value = '', onChange, height = 150 }) => {
  const modules = {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', { list: 'bullet' }],
      [{ header: 1 }, { header: 2 }, { header: [3, 4, 5, 6, false] }],
      [{ align: [] }],
      ['clean'],
    ],
  };

  const formats = ['bold', 'italic', 'underline', 'strike'];
  const placeholder = placeholderValue ?? 'Write some awesome text...';
  const { quill, quillRef } = useQuill({ modules, formats, placeholder });

  useEffect(() => {
    if (!quill) return;
    const current = quill.root ? quill.root.innerHTML : '';
    if ((value || '') !== current) quill.clipboard.dangerouslyPasteHTML(value || '');

    const handler = () => {
      const html = quill.root ? quill.root.innerHTML : '';
      if (onChange) onChange(html);
    };

    quill.on('text-change', handler);
    return () => quill.off('text-change', handler);
  }, [quill, value, onChange]);

  return (
    <div style={{ width: '100%', height }}>
      <div ref={quillRef} />
    </div>
  );
};

export default Quill;
